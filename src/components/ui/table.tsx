"use client";
import { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import { theme } from "@/lib/theme";
import styled from "styled-components";
import NoData from "./no-data";

const Table = forwardRef<TableRef, ITable>(
  (
    { columns = [], items, name = "", onClick, checkable = false, className },
    ref
  ) => {
    const [checkedItems, setCheckedItems] = useState<Set<number>>(new Set());
    const [selectAll, setSelectAll] = useState(false);

    // Initialize state when items change
    useEffect(() => {
      setCheckedItems(new Set());
      setSelectAll(false);
    }, [items]);

    // Handle single row selection
    const handleCheck = (index: number) => {
      setCheckedItems((prev) => {
        const newChecked = new Set(prev);
        if (newChecked.has(index)) {
          newChecked.delete(index);
        } else {
          newChecked.add(index);
        }
        setSelectAll(newChecked.size === items.length); // Update "Select All" status
        return newChecked;
      });
    };

    // Handle "Select All" toggle
    const handleSelectAll = () => {
      setCheckedItems((prev) => {
        if (prev.size === items.length) {
          setSelectAll(false);
          return new Set(); // Uncheck all
        } else {
          setSelectAll(true);
          return new Set(items.map((_, i) => i)); // Select all
        }
      });
    };

    // Expose getCheckedList method
    useImperativeHandle(ref, () => ({
      getCheckedList: () => [...checkedItems].map((index) => items[index]),
    }));

    return (
      <Wrapper
        className={`overflow-x-auto h-full bg-white ${className ? className : ``}`}
      >
        <table
          className={`min-w-full border-collapse bg-white ${items.length === 0 ? `h-full` : ""}`}
        >
          <thead>
            <TableHeaderRow className="text-white uppercase text-sm leading-normal">
              {checkable && (
                <th className="py-3 px-4 text-center">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="cursor-pointer"
                  />
                </th>
              )}
              {columns.map((column, columnKey) => (
                <th
                  key={`${name}_COLUMNS_HEADER_${columnKey}`}
                  className="break-keep py-3 px-4 text-center"
                >
                  {column.label}
                </th>
              ))}
            </TableHeaderRow>
          </thead>
          <tbody className="text-gray-700 text-sm">
            {items.map((item, itemKey) => (
              <TableBodyRow
                key={`${name}_BODY_${itemKey}`}
                onClick={(e) => {
                  if (onClick) onClick(item);
                }}
                className={`border-b transition-all ${onClick ? "cursor-pointer" : ""}`}
              >
                {checkable && (
                  <td className="py-2 px-4 border-b text-center">
                    <input
                      type="checkbox"
                      checked={checkedItems.has(itemKey)}
                      onChange={() => handleCheck(itemKey)}
                      className="cursor-pointer"
                    />
                  </td>
                )}
                {columns.map((column, columnKey) => (
                  <td
                    key={`${name}_BODY_${itemKey}_${columnKey}`}
                    style={column.style}
                    className="py-2 px-4 border-b"
                  >
                    {item[column.value]}
                  </td>
                ))}
              </TableBodyRow>
            ))}
            {items.length === 0 && (
              <tr>
                <td colSpan={columns.length + (checkable ? 1 : 0)}>
                  <NoData />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </Wrapper>
    );
  }
);
const Wrapper = styled.div`
border-radius: 8px 8px 0px 0px;
`
const TableHeaderRow = styled.tr`
  background-color: ${theme.white[3]};
  color: black;
  & > th:first-child {
    border-radius: 8px 0px 0px 8px;
  }
  & > th:last-child {
    border-radius: 0px 8px 8px 0px;
  }
`;

const TableBodyRow = styled.tr`
  &:hover {
    background-color: ${theme.white[2]}60;
  }
`;

Table.displayName = "Table";
export default Table;
