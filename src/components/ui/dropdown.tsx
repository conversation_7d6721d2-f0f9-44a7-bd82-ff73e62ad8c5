import { useEffect, useRef, useState } from "react";
import Button from "@/components/forms/button";
import styled from "styled-components";
import NoData from "./no-data";

/**
 * @param options DropDownOptionType 각 옵션클릭 시 onClick 핸들러 정의할 수 있음
 * @param theme <Button2 /> 기반이라 Button2 theme 그대로 가져감
 * @param children 버튼명. 없다면 선택된 옵션으로 변경됨
 * @param value 현재 값. children이 있으면 화면에 노출되지는 않음
 * @param defaultValue 초기값
 * @returns 드롭다운 컴포넌트
 */
const Dropdown = ({
  options = [],
  value,
  defaultValue,
  onChange,
  children,

  $dropdownAlign,
  $dropdownWidth,
  $padding,
  $minWidth,
  fontSize,
  $margin,
  $theme,
}: DropDownProps) => {
  const ref = useRef<HTMLElement | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const opt = options.find((o) => `${o.value}` === `${defaultValue}`);
  const [selected, setSelected] = useState(opt);

  useEffect(() => {
    const opt = options.find((o) => `${o.value}` === `${value}`);
    setSelected(opt);
  }, [value]);

  useEffect(() => {
    // 외부클릭시 핸들러
    const handleClickOutside = (event: any) => {
      // Do nothing if clicking ref's element or descendent elements
      if (ref && ref.current && !ref.current.contains(event.target) && isOpen) {
        setIsOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [ref, isOpen, setIsOpen]);

  return (
    <div ref={ref as any} className="relative inline-block text-left">
      <Button
        className={`${$minWidth ? `` : `min-w-40`} bg-gray-100 shadow-md rounded-md`}
        onClick={() => setIsOpen(!isOpen)}
        $padding={$padding}
        $fontSize={fontSize}
        $margin={$margin}
        $theme={$theme}
        $minWidth={$minWidth}
      >
        {children || selected?.label}
      </Button>
      {isOpen && (
        <DropdownItemWrapper
          $dropdownAlign={$dropdownAlign}
          $dropdownWidth={$dropdownWidth}
          className="absolute mt-2 w-40 max-h-[270px] overflow-auto bg-white border border-gray-200 rounded-md shadow-lg"
        >
          {options.map((option, index) => (
            <button
              key={index}
              className="block w-full px-4 py-2 text-center hover:bg-gray-100"
              onClick={() => {
                setSelected(option);
                if (option.onClick) {
                  option.onClick(option);
                } else if (onChange) {
                  onChange(option.value);
                }
                setIsOpen(false);
              }}
            >
              {option.label}
            </button>
          ))}
          {options.length === 0 && <NoData />}
        </DropdownItemWrapper>
      )}
    </div>
  );
};

export default Dropdown;

const DropdownItemWrapper = styled.div<{
  $dropdownAlign?: "right" | "left";
  $dropdownWidth?: string;
}>`
  ${(props) => (props.$dropdownAlign === "right" ? "right: 0;" : "")}
  ${(props) => (props.$dropdownWidth ? `width: ${props.$dropdownWidth};` : "")}
  z-index: 1;
`;
