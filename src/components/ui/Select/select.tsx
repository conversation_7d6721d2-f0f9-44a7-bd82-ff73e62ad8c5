import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { theme } from "@/lib/theme";
import styled from "styled-components";

interface ISelect {
  options?: LabelValueType[];
  className?: string;
  defaultValue?: string;
  readOnly?: boolean;
  $width?: string;
  onChange?: (value: string) => void;
}

const Select = forwardRef<SelectRef, ISelect>(
  (
    { options = [], className, defaultValue, readOnly, onChange, $width },
    ref
  ) => {
    const selectRef = useRef<HTMLSelectElement>(null);

    useEffect(() => {
      if (selectRef.current) {
        let value;
        const opt = options.find((o) => o.value === defaultValue);
        if (opt) {
          value = opt.value;
        } else if (options.length > 0) {
          value = options[0]?.value;
        }
        selectRef.current.value = value;
        onChange && onChange(value);
      }
    }, [defaultValue]);

    // Expose clear() function via ref
    useImperativeHandle(ref, () => ({
      clear: () => {
        if (selectRef.current) {
          const resetValue =
            options.find((o) => o.value === defaultValue)?.value ??
            options[0]?.value;
          selectRef.current.value = resetValue;
          onChange && onChange(resetValue);
        }
      },
    }));

    return (
      <SelectWrapper
        ref={selectRef}
        onChange={(e) => [onChange && onChange(e.target.value)]}
        className={className}
        disabled={readOnly}
        defaultValue={defaultValue ?? options[0]?.value}
        $width={$width}
      >
        {options.map((el, idx) => (
          <Option key={`Options_${el.value}_${idx}`} value={el.value}>
            {el.label}
          </Option>
        ))}
      </SelectWrapper>
    );
  }
);

Select.displayName = "Select";
export default Select;

const SelectWrapper = styled.select<{ $width?: string }>`
  ${({ $width }) => ($width ? `width: ${$width};` : "")}
  // height: fit-content;
  min-height: 50px;
  align-self: center;
  min-width: 90px;
  padding: 10px;
  border: 1px solid ${theme.black[6]};
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 16px;
  color: ${theme.black[1]};
  background-color: ${theme.white[0]};
  appearance: none;
  cursor: pointer;

  /* Custom dropdown arrow */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='black'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;

  &:focus {
    outline: none;
    border-color: ${theme.main[4]};
    background-color: ${theme.white[1]};
  }
`;

const Option = styled.option`
  background-color: ${theme.white[0]};
  color: ${theme.black[1]};
  font-size: 16px;
  padding: 10px;
`;
