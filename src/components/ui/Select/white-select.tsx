import { theme } from "@/lib/theme";
import styled from "styled-components";

interface ISelect {
  options: LabelValueType[];
  className?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
}

const WhiteSelect: React.FC<ISelect> = ({
  options,
  className,
  defaultValue,
  onChange,
}) => {
  return (
    <SelectWrapper
      onChange={(e) => onChange && onChange(e.target.value)}
      className={className}
      defaultValue={defaultValue}
    >
      {options.map((el, idx) => {
        return (
          <Option key={`Options_${el}_${idx}`} value={el.value}>
            {el.label}
          </Option>
        );
      })}
    </SelectWrapper>
  );
};

export default WhiteSelect;

const SelectWrapper = styled.select`
  height: fit-content;
  align-self: center;
  flex: 1;
  min-width: 60px;
  width: 100%;
  padding: 12px;
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 16px;
  color: ${theme.black[1]};
  background-color: transparent;
  appearance: none; // 기본 화살표 제거
  cursor: pointer;

  /* 기본 드롭다운 아이콘을 숨기고 커스텀 아이콘 적용 */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='black'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;

  &:focus {
    outline: none;
  }
`;

const Option = styled.option`
  background-color: ${theme.white[0]};
  color: ${theme.black[1]};
  font-size: 16px;
  padding: 10px;
`;
