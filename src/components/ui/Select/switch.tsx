import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import styled from "styled-components";
import { theme } from "@/lib/theme";

interface ISwitch {
  options?: LabelValueType[];
  className?: string;
  defaultValue?: string;
  readOnly?: boolean;
  $width?: string;
  onChange?: (value: string) => void;
}

const Switch = forwardRef<SelectRef, ISwitch>(
  ({ options = [], className, defaultValue, readOnly, onChange, $width }, ref) => {
    const [selected, setSelected] = useState(defaultValue ?? options[0]?.value);

    useEffect(() => {
      if (defaultValue) {
        setSelected(defaultValue);
        onChange && onChange(defaultValue);
      }
    }, [defaultValue]);

    useImperativeHandle(ref, () => ({
      clear: () => {
        const resetValue = options[0]?.value;
        setSelected(resetValue);
        onChange && onChange(resetValue);
      },
    }));

    const handleSelect = (value: string) => {
      if (!readOnly) {
        setSelected(value);
        onChange && onChange(value);
      }
    };

    return (
      <Wrapper className={className} $width={$width}>
        {options.map((option) => (
          <Segment
            key={option.value}
            $selected={selected === option.value}
            onClick={() => handleSelect(option.value)}
          >
            {option.label}
          </Segment>
        ))}
      </Wrapper>
    );
  }
);

Switch.displayName = "SegmentedControl";
export default Switch;

const Wrapper = styled.div<{ $width?: string }>`
  ${({ $width }) => ($width ? `width: ${$width};` : "")}
  display: flex;
  background: ${theme.black[7]};
  border-radius: 10px;
  padding: 4px;
`;

const Segment = styled.div<{ $selected: boolean }>`
  flex: 1;
  text-align: center;
  padding: 6px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: ${({ $selected }) => ($selected ? theme.black[0] : theme.black[3])};
  background: ${({ $selected }) => ($selected ? theme.white[0] : "transparent")};
  transition: all 0.2s ease-in-out;

  &:hover {
    background: ${({ $selected }) => ($selected ? theme.white[0] : theme.black[8])};
  }
`;
