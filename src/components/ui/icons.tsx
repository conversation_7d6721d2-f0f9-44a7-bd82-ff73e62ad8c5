import { PiFloppyDiskBold, Pi<PERSON>istChe<PERSON> } from "react-icons/pi";
import { PiPlus } from "react-icons/pi";
import { FiTrash } from "react-icons/fi";
import { BiDownArrow, BiHistory, BiListUl, BiUpArrow } from "react-icons/bi";
import { GiBoxUnpacking, GiGreekTemple } from "react-icons/gi";
import { FaUserCircle } from "react-icons/fa";
import { FaGear, FaScaleUnbalanced } from "react-icons/fa6";
import {
  MdEditDocument,
  MdOutlineBackpack,
  MdOutlineRateReview,
  MdTune,
} from "react-icons/md";
import { HiOutlineBell, HiOutlineMenu, HiX } from "react-icons/hi";
import { GoSignOut } from "react-icons/go";
import { TbTrash } from "react-icons/tb";

export const FloppyDisk = PiFloppyDiskBold;
export const Trash = () => <TbTrash />;
export const Plus = () => <PiPlus />;
export const DownArrow = () => (
  <BiDownArrow className="text-sm text-gray-600 transition duration-200" />
);

export const UpArrow = () => (
  <BiUpArrow className="text-sm text-gray-600 transition duration-200 " />
);

export const GreekTemple = GiGreekTemple;
export const UserCircle = FaUserCircle;
export const BellIcon = HiOutlineBell;
export const XIcon = HiX;
export const MenuIcon = HiOutlineMenu;
export const Gear = FaGear;
export const SignOutIcon = GoSignOut;

export const Scale = FaScaleUnbalanced;
export const ListCheck = PiListChecks;
export const Tune = MdTune;
export const BoxUnpacking = GiBoxUnpacking;
export const HistoryIcon = BiHistory;
export const DocumentIcon = MdEditDocument;
export const ListIcon = BiListUl;
export const ReviewIcon = MdOutlineRateReview; // MdOutlineRateReview
export const BackPackIcon = MdOutlineBackpack;

interface IconProps {
  size?: number;
  color?: string;
  className?: string;
}

export const BackPackIcon2: React.FC<IconProps> = ({
  size = 24,
  color = "currentColor",
  className = "",
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <g clipPath="url(#clip0)">
      <path
        d="M3.5 7C3.5 4.79086 5.29086 3 7.5 3H16.5C18.7091 3 20.5 4.79086 20.5 7V22C20.5 22.5523 20.0523 23 19.5 23H4.5C3.94772 23 3.5 22.5523 3.5 22V7Z"
        stroke={color}
        strokeWidth="2"
      />
      <path d="M7.25 15H15.25V18" stroke={color} strokeWidth="2" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.75 3V0H9.25V3H5.75Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.75 3V0H18.25V3H14.75Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DrawerIcon: React.FC<IconProps> = ({
  size = 24,
  color = "currentColor",
  className = "",
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M21 8V21H3V8M10 12H14M1 3H23V8H1V3Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PencilIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_7964_1539)">
      <path
        d="M11.3335 2.00001C11.5086 1.82491 11.7165 1.68602 11.9452 1.59126C12.174 1.4965 12.4192 1.44772 12.6668 1.44772C12.9145 1.44772 13.1596 1.4965 13.3884 1.59126C13.6172 1.68602 13.8251 1.82491 14.0002 2.00001C14.1753 2.1751 14.3142 2.38297 14.4089 2.61175C14.5037 2.84052 14.5524 3.08572 14.5524 3.33334C14.5524 3.58096 14.5037 3.82616 14.4089 4.05494C14.3142 4.28371 14.1753 4.49158 14.0002 4.66667L5.00016 13.6667L1.3335 14.6667L2.3335 11L11.3335 2.00001Z"
        stroke="#1E1E1E"
        strokeWidth="1.6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7964_1539">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SearchIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 14L11.1 11.1M12.6667 7.33333C12.6667 10.2789 10.2789 12.6667 7.33333 12.6667C4.38781 12.6667 2 10.2789 2 7.33333C2 4.38781 4.38781 2 7.33333 2C10.2789 2 12.6667 4.38781 12.6667 7.33333Z"
      stroke="#1E1E1E"
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ThumbsUpIcon = ({ fill = "#B4B4B4" }: { fill?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.01264 4.98917L8.70598 1.29584C9.09264 0.902505 9.71931 0.902505 10.1126 1.28917C10.3526 1.52917 10.4526 1.86917 10.386 2.20251L9.82751 4.89491C9.78888 5.08113 9.93107 5.25584 10.1213 5.25584H13.5193C14.9526 5.25584 15.9193 6.72251 15.3593 8.0425L13.186 13.1158C12.9726 13.6025 12.4926 13.9225 11.9593 13.9225H5.95931C5.22598 13.9225 4.62598 13.3225 4.62598 12.5892V5.92917C4.62598 5.57584 4.76598 5.23584 5.01264 4.98917ZM2 5.58984C1.44772 5.58984 1 6.03756 1 6.58984V12.9224C1 13.4746 1.44772 13.9224 2 13.9224H2.66602C3.2183 13.9224 3.66602 13.4746 3.66602 12.9224V6.58984C3.66602 6.03756 3.2183 5.58984 2.66602 5.58984H2Z"
      fill={fill}
    />
  </svg>
);

export const CommentIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4 2C2.34315 2 1 3.34315 1 5V9C1 10.6569 2.34315 12 4 12H6.26795L7.56699 14.25C7.75944 14.5833 8.24056 14.5833 8.43301 14.25L9.73205 12H12C13.6569 12 15 10.6569 15 9V5C15 3.34315 13.6569 2 12 2H4Z"
      fill="#B4B4B4"
    />
  </svg>
);
