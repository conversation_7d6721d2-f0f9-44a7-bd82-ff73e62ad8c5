import { getFileUrl } from "@/lib/file";
import { UserCircle } from "./icons";

const ProfileImage = ({
  fileId,
  fileSn,
  size = 20,
}: {
  fileId?: string;
  fileSn?: number;
  size?: number;
}) => {
  return fileId ? (
    <img
      className="aspect-square rounded-[50%]"
      // style={{ borderRadius: "50%" }}
      src={getFileUrl({ fileId, fileSn })}
      alt="profile-image"
      width={size}
      height={size}
    />
  ) : (
    <UserCircle size={size} className="text-gray-600 aspect-square h-full" />
  );
};

export default ProfileImage;
