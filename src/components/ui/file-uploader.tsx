import React, {
  useState,
  ForwardedRef,
  RefObject,
  ReactNode,
  forwardRef,
  useEffect,
  useRef,
  useImperativeHandle,
} from "react";
import Button from "../forms/button";
import { uploadFiles } from "@/services/fileService";

interface IFileUploader {
  onFileDrop: (uploadedFiles: FileResponse) => void;
  accept?: string;
  multiple?: boolean;
  dragNdrop?: boolean;
  readOnly?: boolean;
  children?: ReactNode;
}

/**
 * FileUploader Component
 * - Supports Drag & Drop (optional)
 * - Customizable children (default: Button)
 * - dragNdrop true 일때 className="border-2 border-dashed" 하면 조금 그럴싸
 *
 * @param {Function} onFileDrop - Callback when files are uploaded
 * @param {string} accept - Allowed file extensions
 * @param {boolean} multiple - Allow multiple file uploads
 * @param {boolean} dragNdrop - Enable drag & drop
 * @param {boolean} readOnly - Disable click
 * @param {ReactNode} children - Custom children (default: Button)
 * @returns {React.ReactNode} File uploader
 */
const FileUploader = forwardRef<FileUploaderRef, IFileUploader>(
  (
    {
      onFileDrop,
      accept = "*",
      multiple = false,
      dragNdrop = false,
      readOnly,
      children,
    },
    ref
  ) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [dragActive, setDragActive] = useState(false);
    const [fileId, setFileId] = useState<string | null>(null);

    useImperativeHandle(ref, () => ({
      ...inputRef.current!,
      getFileId: () => fileId,
    }));

    const handleUpload = async (files: FileList) => {
      try {
        const uploadedFiles = await uploadFiles(Array.from(files)); // Upload API call
        setFileId(uploadedFiles.fileId);
        onFileDrop(uploadedFiles); // Pass uploaded response
      } catch (error) {
        console.error("File upload failed", error);
      }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
      if (!dragNdrop) return;
      e.preventDefault();
      setDragActive(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
      if (!dragNdrop) return;
      e.preventDefault();
      setDragActive(false);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
      if (!dragNdrop) return;
      e.preventDefault();
      setDragActive(false);
      if (e.dataTransfer.files.length > 0) {
        handleUpload(e.dataTransfer.files);
      }
    };

    const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        handleUpload(e.target.files);
      }
    };
    return (
      <div
        className={`${!readOnly && "cursor-pointer"} relative w-fit rounded-lg text-center transition-all`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !readOnly && inputRef.current?.click()}
      >
        <input
          type="file"
          multiple={multiple}
          ref={inputRef}
          readOnly={readOnly}
          className="hidden"
          onChange={handleChange}
          accept={accept}
        />

        {/* Custom Children or Default Button */}
        {children || <Button> 업로드 </Button>}
        <div
          className={`absolute top-0 rigth-0 w-full h-full ${
            dragNdrop && dragActive
              ? "border-blue-500/50 bg-blue-100/50"
              : "border-gray-300"
          }`}
        />
      </div>
    );
  }
);

FileUploader.displayName = "FileUploader";
export default FileUploader;
