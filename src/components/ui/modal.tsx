"use client";

import { useRouter } from "@/i18n/routing";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Title } from "../extra/Common";

interface IModal {
  children: React.ReactNode;
  title?: string;
  isPage?: boolean;
}

/**
 *
 * @returns {React.ReactNode} 모달
 */
const Modal = forwardRef<IModalRef, IModal>(
  ({ children, title, isPage }, ref) => {
    const router = useRouter();
    const [isVisible, setIsVisible] = useState(false);

    // Prevent body scroll when modal is open
    useEffect(() => {
      if (isVisible) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "auto";
      }

      return () => {
        document.body.style.overflow = "auto"; // Reset on unmount
      };
    }, [isVisible]);

    // Animation effect when modal opens
    useEffect(() => {
      if (isPage) {
        setIsVisible(true);
      }
    }, []);

    function closeModal() {
      setIsVisible(false);
      if (isPage) {
        setTimeout(() => {
          router.back(); // Go back to the previous page
        }, 200);
      }
    }
    useImperativeHandle(ref, () => ({
      openModal: () => {
        setIsVisible(true);
      },
      closeModal: () => {
        setIsVisible(false);
      },
    }));
    return (
      <div
        className={`fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300 ${
          isVisible ? "opacity-100" : "opacity-0"
        }`}
        style={{ zIndex: isVisible ? "10" : "-1" }}
      >
        <div
          className={`w-full md:w-fit max-w-[95%] bg-white p-4 md:p-6 rounded-lg shadow-lg transform transition-transform duration-300 ${
            isVisible ? "scale-100" : "scale-95"
          }`}
        >
          <div className="min-h-10">
            {title && <Title>{title}</Title>}
            {/* Close Button */}
            <button
              onClick={closeModal}
              className="absolute z-10 top-6 right-6 text-gray-500 hover:text-gray-800"
            >
              ✕
            </button>
          </div>

          {/* Modal Content */}
          {children}
        </div>
      </div>
    );
  }
);

export default Modal;
