"use client";
import { useTranslations } from "next-intl";

const Pager = ({ totalCount, page, size, onPageChange }: IPager) => {
  const t = useTranslations("Pager");
  const totalPages = Math.ceil(totalCount / size);
  const currentPage = page + 1;

  const handlePrev = () => {
    if (page > 0) onPageChange(page - 1);
  };

  const handleNext = () => {
    if (page < totalPages - 1) onPageChange(page + 1);
  };

  const renderPageNumbers = () => {
    const pages = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => onPageChange(i - 1)}
          className={`w-8 h-8 rounded-md text-sm flex items-center justify-center transition-colors ${
            i === currentPage
              ? "bg-black text-white"
              : "text-black hover:bg-gray-200"
          }`}
        >
          {i}
        </button>
      );
    }
    return pages;
  };

  return (
    <div className="flex items-center justify-center gap-2 py-4">
      {/* Prev */}
      <button
        onClick={handlePrev}
        disabled={page === 0}
        className="text-sm px-2 py-1 text-gray-400 disabled:cursor-not-allowed"
      >
        ← {t("prev")}
      </button>

      {/* Page Numbers */}
      <div className="flex gap-1">{renderPageNumbers()}</div>

      {/* Next */}
      <button
        onClick={handleNext}
        disabled={page >= totalPages - 1}
        className="text-sm px-2 py-1 text-gray-400 disabled:cursor-not-allowed"
      >
        {t("next")} →
      </button>
    </div>
  );
};

export default Pager;
