"use client";

import {
  useState,
  useRef,
  useEffect,
  ReactNode,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  DndContext,
  closestCenter,
  useSensor,
  useSensors,
  PointerSensor,
  TouchSensor,
} from "@dnd-kit/core";
import {
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
  arrayMove,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { CardWrapper } from "../extra/Common";
import Button from "../forms/button";
import { PiPlus } from "react-icons/pi";
import { isMobile } from "react-device-detect";

interface TabProps {
  label: string | React.ReactNode;
  active?: boolean;
  onClick?: () => void;
}

const DraggableTab: React.FC<TabProps & { id: string }> = ({
  id,
  label,
  active,
  onClick,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(
      transform ? ({ ...transform, y: 0 } as any) : null
    ),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="cursor-grab active:cursor-grabbing"
    >
      <button
        onClick={onClick}
        className={`min-h-[100%] w-max px-4 py-2 border-t border-l border-r text-sm ${
          active
            ? "bg-white border-gray-400 text-black"
            : "bg-gray-200 text-gray-600"
        } rounded-t-md focus:outline-none`}
      >
        {label}
      </button>
    </div>
  );
};

const Tabs = forwardRef<TabsRef, ITabs>(
  ({ tabs, onClickAdd, onChange }, ref) => {
    const [tabList, setTabList] = useState<TabType[]>([]);
    const [activeIndex, setActiveIndex] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      setTabList(tabs.map((tab, index) => ({ id: `tab-${index}`, ...tab })));
    }, [tabs]);

    const sensors = useSensors(
      isMobile
        ? useSensor(TouchSensor, {
            activationConstraint: { delay: 400, tolerance: 50 },
          }) // Mobile drag
        : useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
    );

    const handleDragEnd = (event: any) => {
      const { active, over } = event;
      if (!over || active.id === over.id) return;

      const oldIndex = tabList.findIndex((tab) => tab.id === active.id);
      const newIndex = tabList.findIndex((tab) => tab.id === over.id);
      const newTabList = arrayMove(tabList, oldIndex, newIndex);

      setTabList(newTabList);
      const idx = newTabList.findIndex(
        (tab) => tab.id === tabList[activeIndex].id
      );
      setActiveIndex(idx);
      onChange && onChange(idx, newTabList);
    };

    const _onClickAdd = () => {
      onClickAdd && onClickAdd();
      setActiveIndex(tabList.length);
      onChange && onChange(tabList.length);

      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollBy({
            left: containerRef.current.scrollWidth,
            behavior: "smooth",
          });
        }
      }, 100);
    };

    /** Expose methods via ref */
    useImperativeHandle(ref, () => ({
      setTabs: (tabs: TabType[]) => {
        setTabList(tabs.map((tab, index) => ({ id: `tab-${index}`, ...tab })));
      },
      getTabs: () => tabs,
      setActiveTab: (index: number) => {
        if (index >= 0 && index < tabList.length) {
          setActiveIndex(index);
          onChange && onChange(index);
        }
      },
      getActiveTab: () => activeIndex,
    }));

    return (
      <div className="w-full h-full flex flex-col max-w-2xl mx-auto">
        {/* Tab Headers with Drag & Drop */}
        <div className="flex justify-between">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={tabList.map((tab) => tab.id!)}
              strategy={horizontalListSortingStrategy}
            >
              <div
                ref={containerRef}
                className="mt-[.3rem] mr-[.3rem] ml-[.3rem] overflow-x-auto flex space-x-1 border-b border-gray-300 bg-gray-100"
              >
                {tabList.map((tab, index) => (
                  <DraggableTab
                    key={tab.id}
                    id={tab.id!}
                    label={tab.label}
                    active={index === activeIndex}
                    onClick={() => [
                      setActiveIndex(index),
                      onChange && onChange(index),
                    ]}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>

          {onClickAdd && (
            <div className="mt-[.3rem]">
              <Button
                $margin="4px"
                $padding="10px"
                $fontSize="12px"
                onClick={_onClickAdd}
              >
                <PiPlus />
              </Button>
            </div>
          )}
        </div>

        {/* Tab Content */}
        <CardWrapper style={{ maxWidth: "524px" }}>
          {tabList[activeIndex]?.content || (
            <div className="w-full min-h-[200px]" />
          )}
        </CardWrapper>
      </div>
    );
  }
);

Tabs.displayName = "Tabs"; // Prevents Next.js hydration warnings

export default Tabs;
