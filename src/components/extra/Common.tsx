"use client";

import { theme } from "@/lib/theme";
import styled from "styled-components";

export const BoardContainer = styled.div`
  // width: 100%;
  // height: 100%;
  // background: ${theme.white[0]};
`;

export const BoardRow = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 112px;
  padding: 16px;
  gap: 10px;
`;

export const Divider = styled.div`
  border-bottom: 1px solid ${theme.white[4]};
  height: 1px;
`;

export const Seperator = styled.div`
  height: 20px;
`;

export const PageWrapper = styled.main<{ $width?: string }>`
  width: ${({ $width }) => $width || "auto"};
  @media (max-width: 767px) {
    padding: 0 !important;
  }
`;

export const CardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
  margin-top: 10px;
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

export const CardWrapper = styled.section<{
  $margin?: string;
  $padding?: string;
  $isTopFlat?: string;
  $height?: string;
}>`
  display: flex;
  flex-direction: column;
  background: ${theme.white[0]};
  padding: ${(props) => props.$padding || `20px`};
  margin: ${(props) => props.$margin || `auto`};
  border-radius: 8px;
  width: 100%;
  @media (min-width: 767px) {
    max-width: 380px;
  }
  height: ${(props) => props.$height || `100%`};

  ${(props) =>
    props.$isTopFlat &&
    `
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
  `}
`;

export const Title = styled.h2`
  // margin-right: auto;
  font-size: 22px;
  font-weight: bold;
  color: black;
  // align-self: center;
`;

export const SubTitle = styled.h3`
  font-size: 18px;
  font-weight: bold;
  color: black;
`;

export const Row = styled.div<{
  $width?: string;
  $height?: string;
  $margin?: string;
  $padding?: string;
  $gap?: string;
  $align?: string;
  $cursor?: string;
  $justify?: string;
  $radius?: string;
  $background?: string;
  $alignContent?: string;
}>`
  display: flex;
  width: ${(props) => props.$width || `100%`};
  height: ${(props) => props.$height || `auto`};
  gap: ${(props) => props.$gap || `0`};
  padding: ${(props) => props.$padding || `auto`};
  margin: ${(props) => props.$margin || `auto`};
  text-align: ${({ $align }) => $align || `auto`};
  box-sizing: border-box;
  cursor: ${({ $cursor }) => $cursor || `default`};
  justify-content: ${({ $justify }) => $justify || `default`};
  font-size: 16px;
  border-radius: ${(props) => props.$radius || `none`};
  background: ${(props) => props.$background || ``};
  align-self: ${(props) => props.$alignContent || ``};
`;

export const Col = styled.div<{
  $width?: string;
  $height?: string;
  $margin?: string;
  $padding?: string;
  $gap?: string;
  $align?: string;
  $cursor?: string;
  $radius?: string;
  $background?: string;
  $justify?: string;
}>`
  display: flex-column;
  width: ${(props) => props.$width || `100%`};
  height: ${(props) => props.$height || `auto`};
  gap: ${(props) => props.$gap || `0`};
  padding: ${(props) => props.$padding || `auto`};
  margin: ${(props) => props.$margin || `auto`};
  text-align: ${({ $align }) => $align || `auto`};
  box-sizing: border-box;
  font-size: 14px;
  cursor: ${({ $cursor }) => $cursor || `default`};
  border-radius: ${(props) => props.$radius || `none`};
  background: ${(props) => props.$background || ``};
  justify-content: ${({ $justify }) => $justify || `default`};
`;

export const Between = styled.div<{
  $width?: string;
  $margin?: string;
  $padding?: string;
  $gap?: string;
  $align?: string;
  $cursor?: string;
}>`
  display: flex;
  width: ${(props) => props.$width || `100%`};
  gap: ${(props) => props.$gap || `0`};
  padding: ${(props) => props.$padding || `auto`};
  margin: ${(props) => props.$margin || `auto`};
  text-align: ${({ $align }) => $align || `auto`};
  box-sizing: border-box;
  justify-content: space-between;
  cursor: ${({ $cursor }) => $cursor || `default`};
  font-size: 16px;
`;

export const CustomText = styled.div<{
  $margin?: string;
  $color?: string;
  $fontSize?: string;
  $fontWeight?: string;
  $maxlines?: number;
  $align?: string;
  $padding?: string;
  $cursor?: string;
  $height?: string;
  $alignContent?: string;
}>`
  margin: ${(props) => props.$margin || `0`};
  padding: ${({ $padding }) => $padding || `0`};
  color: ${({ $color }) => $color || theme.black[0]};
  font-size: ${(props) => props.$fontSize || `14px`};
  font-weight: ${(props) => props.$fontWeight || `400`};
  text-align: ${({ $align }) => $align || `auto`};
  align-content: ${({ $alignContent }) => $alignContent || `center`};
  align-self: center;
  cursor: ${({ $cursor }) => $cursor || `default`};
  height: ${({ $height }) => $height || `auto`};
  ${(props) =>
    props.$maxlines &&
    `
    display: -webkit-box;
    -webkit-line-clamp: ${props.$maxlines};
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  `}
`;

export const ImageWrapper = styled.div<{
  $radius?: string;
  $ratio?: string;
  $align?: string;
  $width?: string;
  $padding?: string;
  $margin?: string;
  $height?: string;
  $cursor?: string;
}>`
  position: relative;
  width: ${({ $width }) => $width || `100%`};
  height: ${({ $height }) => $height || `100%`};
  max-width: 480px;
  aspect-ratio: ${(props) => props.$ratio || `16 / 9`};
  border-radius: ${(props) => props.$radius || `8px`};
  text-align: ${({ $align }) => $align || `auto`};
  overflow: hidden;
  padding: ${(props) => props.$padding || `5px`};
  margin: ${(props) => props.$margin || `0`};
  img {
    cursor: ${({ $cursor }) => $cursor || `default`};
  }
`;

export const TabsContainer = styled.div`
  display: flex;
  gap: 10px;
  border-bottom: 2px solid #ddd;
  padding: 10px 0;
`;

export const TabButton = styled.button<{ $isActive: boolean }>`
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  background: ${({ $isActive }) =>
    $isActive ? theme.black[0] : theme.white[0]};
  color: ${({ $isActive }) => ($isActive ? theme.white[0] : theme.black[0])};
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;

  ${({ $isActive }) =>
    !$isActive
      ? `&:hover {
      background: ${theme.black[3]};
      color: ${theme.white[0]};
    `
      : ``}
  }
`;

export const Tag = styled.div<{ $isSelected?: boolean }>`
  width: auto;
  border-radius: 34px;
  gap: 8px;
  padding-top: 12px;
  padding-right: 16px;
  padding-bottom: 12px;
  padding-left: 16px;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0%;
  background: #e7e9eb;
  font-weight: 500;
  ${({ $isSelected }) =>
    $isSelected &&
    `
      background: #1ceb80;
      font-weight: 700;
  `}
`;
