"use client";

import React, { useState } from "react";
import styled from "styled-components";

export const Tag = styled.div<{ $isSelected?: boolean }>`
  width: auto;
  border-radius: 34px;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0%;
  background: #e7e9eb;
  font-weight: 500;
  cursor: pointer;
  ${({ $isSelected }) =>
    $isSelected &&
    `
      background: #1ceb80;
      font-weight: 700;
  `}
`;

const TagList = ({ tagNames }: { tagNames: string[] }) => {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]
    );
  };

  return (
    <div style={{ display: "flex", flexWrap: "wrap", gap: "10px" }}>
      {tagNames.map((tag) => (
        <Tag
          key={tag}
          $isSelected={selectedTags.includes(tag)}
          onClick={() => toggleTag(tag)}
        >
          {tag}
        </Tag>
      ))}
    </div>
  );
};

export default TagList;
