"use client";

import {
  Between,
  BoardContainer,
  <PERSON><PERSON>ow,
  <PERSON><PERSON>ontainer,
  Card<PERSON>rapper,
  Col,
  CustomText,
  Divider,
  ImageWrapper,
  Row,
  Seperator,
  TabButton,
  TabsContainer,
  Tag,
  Title,
} from "@/components/extra/Common";
import Button from "@/components/forms/button";
import { formatCompactNumber } from "@/lib/number";
import { theme } from "@/lib/theme";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Link, useRouter } from "@/i18n/routing";
import { getPostCategories, getPosts } from "@/services/postService";
import { getFileUrl } from "@/lib/file";
import { formatDate, timeAgo } from "@/lib/date";
import { FRONT_URL_PATH } from "@/lib/constants";
import Pager from "@/components/ui/pager";
import NoData from "@/components/ui/no-data";
import { stripHtmlTags } from "@/lib/text";
import Loading from "@/components/ui/loading";
import TagList from "@/components/extra/TagList";
import Select from "@/components/ui/Select/select";

interface PostCommonProps {
  selectOptions: LabelValueType[];
  selectedTab: string;
  setSelectedTab: React.Dispatch<React.SetStateAction<string>>;
  filteredData: PostListType[];
  pagerProps: IPager;
  loading: boolean;
  router: any;
}

const PCPost: React.FC<PostCommonProps> = ({
  selectOptions,
  selectedTab,
  setSelectedTab,
  filteredData,
  pagerProps,
  loading,
  router,
}) => {
  const t = useTranslations("Posts");
  return <></>;
};

export default PCPost;

const ProfileSection = ({
  profileSrc,
  authorName,
  createdAt,
}: {
  profileSrc: string;
  authorName: string;
  createdAt: string;
}) => (
  <Row $margin="5px 0">
    <img
      src={profileSrc}
      alt="profile_01"
      width={20}
      style={{ borderRadius: "50%" }}
    />
    <CustomText
      $fontWeight="500"
      $margin="0 10px"
      $fontSize="14px"
      $color={theme.black[4]}
    >
      {authorName}
    </CustomText>
  </Row>
);

const BoardSection = ({
  title,
  content,
}: {
  title: string;
  content: string;
}) => (
  <Col $margin="0" $cursor="pointer">
    <CustomText $fontSize="16px" $fontWeight="600">
      {title}
    </CustomText>
    {/* <CustomText $maxlines={1} dangerouslySetInnerHTML={{ __html: content }} /> */}
  </Col>
);

const Thumbnail = ({ thumbnailSrc }: { thumbnailSrc: string }) => (
  <div style={{ justifyItems: "right", alignContent: "center" }}>
    <img
      // src={thumbnailSrc}
      src={"/images/example/lol.png"}
      alt="thumbnail"
      width={80}
      height={80}
    />
  </div>
);

const InfoSection = ({
  likeCnt,
  commentCnt,
  viewCnt,
  createdAt,
}: {
  likeCnt: string;
  commentCnt: string;
  viewCnt: string;
  createdAt: string;
}) => (
  <Row $margin="0" $alignContent="center">
    <CustomText $color={theme.black[4]}>{formatDate(createdAt)}</CustomText>
    <Row
      $width="14px"
      $height="12px"
      $margin="0 5px 0 10px"
      $alignContent="center"
    >
      <img src={"/icons/like.png"} alt="like" />
    </Row>
    <CustomText
      $fontSize="12px"
      $padding="2px 0 0 0"
      $margin="0 8px 0 0"
      $color={theme.black[4]}
    >
      {likeCnt}
    </CustomText>

    <Row
      $width="14px"
      $height="12px"
      $margin="0 5px 0 10px"
      $alignContent="center"
    >
      <img src={"/icons/comment.png"} alt="comment" />
    </Row>
    <CustomText
      $fontSize="12px"
      $padding="2px 0 0 0"
      $margin="0 8px 0 0"
      $color={theme.black[4]}
    >
      {commentCnt}
    </CustomText>

    <Row
      $width="14px"
      $height="12px"
      $margin="0 5px 0 10px"
      $alignContent="center"
    >
      <img src={"/icons/view.png"} alt="view" />
    </Row>
    <CustomText
      $fontSize="12px"
      $padding="2px 0 0 0"
      $margin="0 8px 0 0"
      $color={theme.black[4]}
    >
      {viewCnt}
    </CustomText>
  </Row>
);
