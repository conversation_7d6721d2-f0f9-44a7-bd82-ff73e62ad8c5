"use client";

import { ReactNode } from "react";
import styled from "styled-components";
import Header from "./header";
import Footer from "./footer";
import StyledComponentsRegistry from "@/lib/registry";
import { theme } from "@/lib/theme";
import useAuthToken from "@/hooks/useAuthToken";
import Loading from "../ui/loading";

export const SCProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const _prepared = useAuthToken();
  return (
    <StyledComponentsRegistry>
      <div
        className={`z-50 bg-gray-300/40 position fixed top-0 right-0 w-[100vw] h-[100vh] ${_prepared ? `hidden` : ""}`}
      >
        <Loading className="w-fit content-center" />
      </div>
      <Container>
        <Header />
        <Content>{children}</Content>
        <Footer />
      </Container>
    </StyledComponentsRegistry>
  );
};

const Container = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  @media (min-width: 767px) {
    // width: 1100px;
    // max-width: 480px; /* 모바일 웹뷰 기준 */
    // min-height: 100vh; /* 화면 전체 높이 확보 */
  }
  margin: 0 auto;
  background: ${theme.white[2]};
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
`;

const Content = styled.div`
  flex: 1; /* 남은 공간을 차지하여 푸터를 밀어냄 */
  & main {
    width: 1100px;
    min-height: 100vh;
    // padding: 16px;
    margin: auto;
    @media (max-width: 767px) {
      margin: unset;
      width: auto;
    }
  }
`;
