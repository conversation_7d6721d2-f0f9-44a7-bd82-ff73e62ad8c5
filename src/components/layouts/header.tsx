"use client";
import { useEffect, useRef, useState } from "react";
import useAuthToken from "@/hooks/useAuthToken";
import { Link, useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";
import { signOut } from "@/services/authService";
import { useUserStore } from "@/store/userStore";
import { useTranslations } from "next-intl";
import Image from "next/image";
import {
  GreekTemple,
  UserCircle,
  Scale,
  ListCheck,
  Tune,
  HistoryIcon,
  DocumentIcon,
  ReviewIcon,
  ListIcon,
  BackPackIcon,
  BellIcon,
  MenuIcon,
  XIcon,
  Gear,
  SignOutIcon,
} from "../ui/icons";
import { motion, AnimatePresence } from "framer-motion";
import useComponentSize from "@/hooks/useComponentSize";
import { getFileUrl } from "@/lib/file";
import Button from "../forms/button";

const Header = () => {
  const t = useTranslations("Route");
  const tp = useTranslations("Header");
  const ref = useRef<HTMLElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const { width } = useComponentSize(ref);

  // Prevent body scroll when the menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  }, [isOpen]);

  // Close menu when screen size increases
  useEffect(() => {
    if (width > 767 && isOpen) {
      setIsOpen(false);
    }
  }, [width]);

  const user = useUserStore((state) => state.user);
  const _prepared = useUserStore((state) => state._prepared);
  const clearUser = useUserStore((state) => state.clearUser);
  const router = useRouter();

  const handleSignOut = async (e: any) => {
    e.preventDefault();
    router.push(FRONT_URL_PATH.INDEX);
    alert(tp("complete-sign-out"));
    setTimeout(async () => {
      await signOut();
      localStorage.removeItem("token");
      clearUser();
    }, 300);
  };

  const navs = [
    {
      Icon: Scale, // Equipment (Backpack Icon)
      href: FRONT_URL_PATH.GEAR_MEASUREMENT,
      text: t("equipment"),
    },
    {
      Icon: ListCheck,
      href: FRONT_URL_PATH.GEAR,
      text: t("equipment-list"),
      loginRequired: true,
    },
    {
      Icon: Tune,
      href: FRONT_URL_PATH.GEAR_PRESET,
      text: t("equipment-preset"),
      loginRequired: true,
    },
    {
      Icon: HistoryIcon,
      href: FRONT_URL_PATH.GEAR_HISTORY,
      text: t("equipment-history"),
      loginRequired: true,
    },
    {
      Icon: DocumentIcon, // Posts (Document Icon)
      href: FRONT_URL_PATH.POSTS,
      text: t("posts"),
      subMenus: [
        {
          Icon: ListIcon,
          href: FRONT_URL_PATH.POSTS,
          text: t("posts-list"),
          loginRequired: false,
        },
        {
          Icon: ListIcon,
          href: FRONT_URL_PATH.POSTS_MY,
          text: t("posts-my"),
          loginRequired: false,
        },
        // {
        //   Icon: ReviewIcon,
        //   href: FRONT_URL_PATH.POSTS_REVIEWS,
        //   text: t("posts-review"),
        //   loginRequired: false,
        // },
        // {
        //   Icon: BackPackIcon,
        //   href: FRONT_URL_PATH.POSTS_WHATSINMYBACKPACK,
        //   text: t("posts-whatsinmybackpack"),
        //   loginRequired: false,
        // },
      ],
    },
  ];

  return (
    <header
      ref={ref}
      className="w-full bg-white p-4 flex items-center justify-between relative"
    >
      <div className="flex items-center space-x-5">
        {/* Logo */}
        <Link
          href={FRONT_URL_PATH.INDEX}
          className="flex items-center space-x-2"
        >
          <Image
            src="/logo-l.png"
            alt="Logo"
            // className="aspect-square"
            width={150}
            height={48}
          />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex gap-4 items-center">
          <ul className="flex space-x-6">
            {navs.map((nav, index) => (
              <li key={`HEADER_NAV_${index}`} className="relative group">
                <Link
                  href={
                    nav.loginRequired && !user
                      ? FRONT_URL_PATH.SIGNIN
                      : nav.href
                  }
                  className="transition duration-200 px-3 py-2 rounded-md hover:font-bold hover:bg-gray-100"
                >
                  {nav.text}
                </Link>

                {/* Desktop Dropdown */}
                {nav.subMenus && (
                  <div className="z-10 absolute left-0 pt-2 w-48 hidden group-hover:block">
                    <motion.ul
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="bg-white shadow-lg border rounded-md"
                    >
                      {nav.subMenus.map((subMenu, subIdx) => (
                        <li
                          key={`SUBMENU_${index}_${subIdx}`}
                          className="border-b last:border-0"
                        >
                          <Link
                            href={
                              subMenu.loginRequired && !user
                                ? FRONT_URL_PATH.SIGNIN
                                : subMenu.href
                            }
                            className="block px-4 py-2 hover:font-bold hover:bg-gray-100"
                          >
                            {subMenu.text}
                          </Link>
                        </li>
                      ))}
                    </motion.ul>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
      <div className="hidden md:flex gap-4 items-center">
        {/* Profile & Notifications */}
        <div className="flex items-center space-x-4">
          {/* Profile Dropdown */}
          {!_prepared ? (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gray-200 animate-pulse rounded-full"></div>
              <div className="w-10 h-4 bg-gray-200 animate-pulse rounded-md"></div>
            </div>
          ) : user ? (
            <div className="relative group">
              <button className="hidden md:flex items-center space-x-2">
                {user?.image ? (
                  <img
                    style={{ borderRadius: "50%", aspectRatio: 1 }}
                    src={getFileUrl(user.image)}
                    width={25}
                    height={25}
                    alt="profile-image"
                  />
                ) : (
                  <UserCircle className="text-2xl text-gray-600" />
                )}
                <span>{user.name}</span>
              </button>

              {/* Dropdown */}
              <div className="z-10 absolute right-0 pt-2 w-32 hidden group-hover:block">
                <div className="bg-white border border-gray-300 shadow-md rounded-md">
                  <ul className="text-sm text-gray-700">
                    <li>
                      <Link
                        href={FRONT_URL_PATH.SETTINGS}
                        className="block px-4 py-2 hover:bg-gray-100"
                      >
                        {t("settings")}
                      </Link>
                    </li>
                    <li>
                      <button
                        className="w-full text-left px-4 py-2 hover:bg-gray-100"
                        onClick={handleSignOut}
                      >
                        {tp("sign-out")}
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex gap-2">
              <Button
                $theme="gray"
                $padding="8px 12px"
                style={{ height: "fit-content" }}
                onClick={() => router.push(FRONT_URL_PATH.SIGNIN)}
              >
                {tp("sign-in")}
              </Button>
              <Button
                $padding="8px 12px"
                style={{ height: "fit-content" }}
                onClick={() => router.push(FRONT_URL_PATH.SIGNUP)}
              >
                {tp("sign-up")}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu Button */}
      <button className="md:hidden z-10" onClick={() => setIsOpen(!isOpen)}>
        {isOpen ? <></> : <MenuIcon size={24} />}
      </button>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 z-30"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{ duration: 0.3 }}
              className="fixed top-0 left-0 w-64 h-full bg-white shadow-lg p-5 flex flex-col z-40"
            >
              <button
                className="absolute top-6 right-6"
                onClick={() => setIsOpen(false)}
              >
                <XIcon size={24} />
              </button>

              {/* Mobile Profile & Logo */}
              <div className="mb-3">
                <Image
                  src="/logo.png"
                  alt="Logo"
                  className="aspect-square"
                  width={48}
                  height={48}
                />
              </div>
              <div className="px-4 my-4">
                {!_prepared ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gray-200 animate-pulse rounded-full"></div>
                    <div className="w-10 h-4 bg-gray-200 animate-pulse rounded-md"></div>
                  </div>
                ) : user ? (
                  <div className="flex gap-3">
                    {user?.image ? (
                      <img
                        style={{ borderRadius: "50%", aspectRatio: 1 }}
                        src={getFileUrl(user.image)}
                        width={25}
                        height={25}
                        alt="profile-image"
                      />
                    ) : (
                      <UserCircle className="text-2xl text-gray-600" />
                    )}
                    <span>{user.name}</span>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <Link
                      href={FRONT_URL_PATH.SIGNIN}
                      onClick={() => setIsOpen(false)}
                    >
                      <span className="transition hover:bg-gray-100 cursor-pointer">
                        {tp("sign-in")}
                      </span>
                    </Link>
                    <Link
                      href={FRONT_URL_PATH.SIGNUP}
                      onClick={() => setIsOpen(false)}
                    >
                      <span className="transition hover:bg-gray-100 cursor-pointer">
                        {tp("sign-up")}
                      </span>
                    </Link>
                  </div>
                )}
              </div>
              <ul className="mt-3 space-y-4">
                {navs.map(({ Icon, ...nav }, index) => (
                  <li
                    key={`HEADER_NAV_MOBILE_${index}`}
                    onClick={() => {
                      if (nav.subMenus) {
                        setActiveMenu(
                          nav.text === activeMenu ? null : nav.text
                        );
                      } else {
                        router.push(
                          nav.loginRequired && !user
                            ? FRONT_URL_PATH.SIGNIN
                            : nav.href
                        );
                        setIsOpen(false);
                      }
                    }}
                  >
                    <span className="flex items-center gap-3 px-4 py-2 text-lg transition hover:bg-gray-100 cursor-pointer">
                      <Icon fontSize={22} />
                      {nav.text}
                    </span>
                    {activeMenu === nav.text && nav.subMenus && (
                      <ul className="pl-4 mt-1 space-y-2">
                        {nav.subMenus.map(({ Icon, ...subMenu }, subIdx) => (
                          <li key={`SUBMENU_MOBILE_${index}_${subIdx}`}>
                            <Link
                              href={
                                subMenu.loginRequired && !user
                                  ? FRONT_URL_PATH.SIGNIN
                                  : subMenu.href
                              }
                              onClick={() => setIsOpen(false)}
                              className="flex items-center gap-3 px-4 py-2 hover:bg-gray-100"
                            >
                              <Icon fontSize={20} />
                              {subMenu.text}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </li>
                ))}
              </ul>

              {user && (
                <ul className="mt-3 text-sm text-gray-700">
                  <li>
                    <Link
                      href={FRONT_URL_PATH.SETTINGS}
                      className="flex items-center gap-3 px-4 py-2 hover:bg-gray-100"
                      onClick={() => setIsOpen(false)}
                    >
                      <Gear />
                      {t("settings")}
                    </Link>
                  </li>
                  <li>
                    <button
                      className="flex items-center gap-3 w-full text-left px-4 py-2 hover:bg-gray-100"
                      onClick={(e) => [handleSignOut(e), setIsOpen(false)]}
                    >
                      <SignOutIcon />
                      {tp("sign-out")}
                    </button>
                  </li>
                </ul>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;
