import { theme } from "@/lib/theme";
import styled from "styled-components";

const Footer = () => {
  return (
    <FooterWrapper>
      &copy; {new Date().getFullYear()} Hike a good day. All rights reserved.
      <br></br>
      문의: <EMAIL>
    </FooterWrapper>
  );
};

export default Footer;

const FooterWrapper = styled.footer`
  width: 100%;
  padding: 16px;
  background-color: ${theme.white[4]};
  text-align: center;
  font-size: 14px;
  color: #666;
`;
