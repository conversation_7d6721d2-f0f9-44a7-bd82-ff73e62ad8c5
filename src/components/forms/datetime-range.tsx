"use client";
import { forwardRef, useRef, useImperative<PERSON><PERSON><PERSON>, ChangeEvent } from "react";
import styled from "styled-components";
import { theme } from "@/lib/theme";
import { useTranslations } from "next-intl";

const RangeWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
`;

const InputWrapper = styled.div`
  position: relative;
  flex: 1;
`;

const StyledInput = styled.input`
  width: 100%;
  padding: 10px;
  padding-top: 20px;
  height: 50px;
  border: 1px solid ${theme.black[6]};
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 16px;
  color: ${theme.black[0]};
  background-color: white;

  &:focus {
    outline: none;
    border-color: ${theme.main[4]};
  }
`;

const Label = styled.label`
  position: absolute;
  left: 10px;
  top: 4px;
  font-size: 12px;
  color: ${theme.black[5]};
  pointer-events: none;
`;

interface IDateTimeRange {
  type?: "date" | "datetime-local";
  defaultValue?: { start?: string; end?: string };
  labelStart?: string;
  labelEnd?: string;
  readOnly?: boolean;
  onChange?: ({ start, end }: { start?: string; end?: string }) => void;
}

const DateTimeRange = forwardRef<DateTimeRangeRef, IDateTimeRange>(
  (
    {
      type = "date",
      defaultValue = {},
      labelStart,
      labelEnd,
      onChange,
      readOnly,
    },
    ref
  ) => {
    const t = useTranslations("DatetimeRange");
    const startRef = useRef<HTMLInputElement>(null);
    const endRef = useRef<HTMLInputElement>(null);

    useImperativeHandle(ref, () => ({
      clear: () => {
        if (startRef.current) startRef.current.value = "";
        if (endRef.current) endRef.current.value = "";
      },
    }));

    const _onChange = (e: ChangeEvent) => {
      e.preventDefault();
      e.stopPropagation;

      if (startRef && endRef && onChange) {
        onChange({
          start: startRef.current?.value,
          end: endRef.current?.value,
        });
      }
    };

    return (
      <RangeWrapper>
        {/* Start Date/Datetime Input */}
        <InputWrapper>
          <Label>{labelStart || t(`start-${type}`)}</Label>
          <StyledInput
            ref={startRef}
            type={type}
            defaultValue={defaultValue?.start}
            onChange={_onChange}
            readOnly={readOnly}
          />
        </InputWrapper>

        <span>~</span>

        {/* End Date/Datetime Input */}
        <InputWrapper>
          <Label>{labelEnd || t(`end-${type}`)}</Label>
          <StyledInput
            ref={endRef}
            type={type}
            defaultValue={defaultValue?.end}
            onChange={_onChange}
            readOnly={readOnly}
          />
        </InputWrapper>
      </RangeWrapper>
    );
  }
);

DateTimeRange.displayName = "DateTimeRange";

export default DateTimeRange;
