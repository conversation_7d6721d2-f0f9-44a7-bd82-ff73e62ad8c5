"use client";
import { useRef, useEffect, useState } from "react";

interface AutoResizeInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  minWidth?: number; // Minimum width of the input
  defaultValue?: string;
}

const AutoResizeInput: React.FC<AutoResizeInputProps> = ({
  minWidth = 100,
  className,
  defaultValue,
  onChange,
  ...props
}) => {
  const spanRef = useRef<HTMLSpanElement>(null);
  const [value, setValue] = useState<string | undefined>(defaultValue);
  // useEffect(() => {
  //   setValue(`${props.value || ""}`);
  // }, [props.value]);
  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);
  return (
    <div className="relative">
      {/* Hidden span to calculate width dynamically */}
      <span ref={spanRef} className="invisible whitespace-pre px-2">
        {value || ""}
      </span>

      {/* Resizable Input */}
      <input
        type="text"
        onChange={(e) => [setValue(e.target.value), onChange && onChange(e)]}
        style={{
          background: "transparent",
          width: `${(spanRef.current?.offsetWidth || 0) > minWidth ? spanRef.current?.offsetWidth : 100}px`,
        }}
        defaultValue={defaultValue}
        className={`absolute bottom-0 left-0 focus:outline-none ${className}`}
        // style={{ width: `${inputWidth}px`, minWidth: `${minWidth}px` }}
        {...props}
      />
    </div>
  );
};

export default AutoResizeInput;
