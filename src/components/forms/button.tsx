"use client";
import { theme } from "@/lib/theme";
import styled from "styled-components";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {}

const Button = styled.button<{
  $padding?: string;
  $fontSize?: string;
  $margin?: string;
  $theme?: string;
  $minWidth?: string;
  $radius?: string;
}>`
  ${(props) => (props.$minWidth ? `min-width: ${props.$minWidth};` : ``)}
  padding: ${(props) => (props.$padding ? props.$padding : `12px`)};
  border: none;
  border-radius: ${(props) => (props.$radius ? props.$radius : "5px")};
  cursor: pointer;
  font-size: ${(props) => (props.$fontSize ? props.$fontSize : `16px`)};
  font-weight: 600;
  margin: ${(props) => (props.$margin ? props.$margin : `0px`)};
  background: ${(props) =>
    props.$theme === "gray"
      ? theme.white[3]
      : props.$theme === "red"
        ? theme.red[2]
        : props.$theme === "blue"
          ? theme.blue[2]
          : props.$theme === "amber"
            ? theme.amber[2]
            : props.$theme === "black"
              ? theme.black[0]
              : props.$theme === "white"
                ? theme.white[0]
                : theme.main[5]};
  color: ${(props) =>
    props.$theme === "white" || props.$theme === "gray" || !props.$theme
      ? "black"
      : "white"};
  &:hover {
    background: ${(props) =>
      props.$theme === "gray"
        ? theme.white[4]
        : props.$theme === "red"
          ? theme.red[4]
          : props.$theme === "blue"
            ? theme.blue[4]
            : props.$theme === "amber"
              ? theme.amber[4]
              : props.$theme === "black"
                ? theme.black[4]
                : props.$theme === "white"
                  ? theme.white[4]
                  : theme.main[6]};
    // color: white;
    transition: 0.5s;
  }
`;
// const Button: React.FC<ButtonProps> = ({ className, children, ...props }) => {
//   return (
//     <button
//       className={`
//         px-4 py-3 bg-orange-400 text-white border-none rounded-md cursor-pointer
//         text-base font-semibold transition duration-500 hover:bg-orange-600
//         ${className || ""}
//       `}
//       {...props}
//     >
//       {children}
//     </button>
//   );
// };

export default Button;

export const BubbleButton = styled.button<{
  $theme?: string;
}>`
  min-width: 100px;
  padding: 6px 12px;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  margin: 0px;
  background: ${(props) =>
    props.$theme === "gray"
      ? theme.white[2]
      : props.$theme === "red"
        ? theme.red[2]
        : props.$theme === "blue"
          ? theme.blue[2]
          : props.$theme === "amber"
            ? theme.amber[2]
            : props.$theme === "black"
              ? theme.black[0]
              : props.$theme === "white"
                ? theme.white[0]
                : theme.main[5]};
  color: ${(props) =>
    props.$theme === "white" || props.$theme === "gray" || !props.$theme
      ? "black"
      : "white"};
  &:hover {
    background: ${(props) =>
      props.$theme === "gray"
        ? theme.white[4]
        : props.$theme === "red"
          ? theme.red[4]
          : props.$theme === "blue"
            ? theme.blue[4]
            : props.$theme === "amber"
              ? theme.amber[4]
              : props.$theme === "black"
                ? theme.black[4]
                : props.$theme === "white"
                  ? theme.white[4]
                  : theme.main[6]};
    // color: white;
    transition: 0.5s;
  }
`;
