"use client";
import { forwardRef, useRef, useState, useImperativeHandle } from "react";
import styled from "styled-components";
import { theme } from "@/lib/theme";

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  margin: 10px 0;
`;

const StyledInput = styled.input`
  width: 100%;
  flex: 1;
  min-width: 0;
  padding: 10px;
  padding-top: 20px;
  height: 50px;
  border: 1px solid ${theme.black[6]};
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 16px;
  caret-color: black;
  color: ${theme.black[0]};
  background-color: white;

  &:focus {
    outline: none;
    border-color: ${theme.main[4]};
  }
`;

const Label = styled.label`
  position: absolute;
  left: 10px;
  top: 4px;
  transform: none;
  font-size: 12px;
  color: ${theme.black[5]};
  transition: all 0.3s ease-in-out;
  pointer-events: none;
`;

interface IDateTimeInput extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  type?: "date" | "datetime-local";
}
const Datetime = forwardRef<DateTimeInputRef, IDateTimeInput>(
  ({ label, type = "date", className, ...props }, ref) => {
    const inputRef = useRef<HTMLInputElement>(null);

    useImperativeHandle(ref, () => ({
      clear: () => {
        if (inputRef.current) {
          inputRef.current.value = "";
        }
      },
    }));

    return (
      <InputWrapper className={className}>
        <Label>{label}</Label>
        <StyledInput ref={inputRef} type={type} {...props} />
      </InputWrapper>
    );
  }
);

Datetime.displayName = "Datetime";

export default Datetime;
