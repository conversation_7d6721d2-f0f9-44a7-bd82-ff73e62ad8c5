"use client";
import {
  forwardRef,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
} from "react";
import styled from "styled-components";
import { theme } from "@/lib/theme";

interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}
const StyledTextarea = styled.textarea<{ $padding?: string }>`
  width: 100%;
  // min-height: 100px;
  box-sizing: border-box;
  font-size: 16px;
  caret-color: black;
  color: ${theme.black[0]};
  resize: none;

  &:focus {
    outline: none;
    // border-color: ${theme.main[4]};
  }
`;

const Textarea = forwardRef<TextareaRef, TextareaProps>(
  ({ placeholder, className, onChange, ...props }, ref) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    useImperativeHandle(ref, () => ({
      focus: () => textareaRef.current?.focus(),
      clear: () => {
        if (textareaRef.current) {
          textareaRef.current.value = "";
        }
      },
    }));

    return (
      <StyledTextarea
        ref={textareaRef}
        placeholder={placeholder}
        {...props}
        onChange={(e) => {
          if (onChange) {
            onChange(e);
          }
          e.target.style.height = "auto";
          e.target.style.height = `${e.target.scrollHeight}px`;
        }}
      />
    );
  }
);

Textarea.displayName = "Textarea";

export default Textarea;
