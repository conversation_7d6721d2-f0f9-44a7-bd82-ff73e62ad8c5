"use client";
import {
  forwardRef,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  useMemo,
  useRef,
  useState,
} from "react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";
import {
  getFileSnFromFile,
  getFileSnFromFileUrl,
  getFileUrl,
} from "@/lib/file";
import "./index.css";
import { uploadFiles } from "@/services/fileService";
import { theme } from "@/lib/theme";

const formats = [
  "font",
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list",
  "indent",
  "link",
  "align",
  "color",
  "background",
  "size",
  "image",
  "video",
];

const CustomToolbar = () => (
  <>
    <div id="toolbar">
      <span className="ql-formats">
        <select className="ql-size" defaultValue="medium">
          <option value="small">Small</option>
          <option value="medium">Medium</option>
          <option value="large">Large</option>
          <option value="huge">Huge</option>
        </select>
        <select className="ql-header">
          <option value="1">Header 1</option>
          <option value="2">Header 2</option>
          <option value="3">Header 3</option>
          <option value="4">Header 4</option>
          <option value="5">Header 5</option>
          <option value="6">Header 6</option>
        </select>
      </span>
      <span className="ql-formats">
        <button className="ql-bold" />
        <button className="ql-italic" />
        <button className="ql-underline" />
        <button className="ql-strike" />
        <button className="ql-blockquote" />
      </span>
      <span className="ql-formats">
        <select className="ql-color" />
        <select className="ql-background" />
      </span>
      <span className="ql-formats">
        <button className="ql-image" />
        <button className="ql-video" />
      </span>
      <span className="ql-formats">
        <button className="ql-clean" />
      </span>
    </div>
  </>
);

/**
 * Quill Editor
 *
 * 에디터 내 값 변경시(onChange) onChange 함수 trigger
 * useRef() 로 값 받아서 사용하는 것 권장
 *
 * <AUTHOR>
 * @param onChange onChange
 * @returns
 */
const _TextEditor = forwardRef<TextEditorRef, ITextEditor>(
  ({ onChange = () => {}, defaultValue, defaultImage, ...rest }, ref) => {
    const quillRef = useRef<ReactQuill>(null);
    // const fileRef = useRef<FileType | null>(null);
    const fileIdRef = useRef<string>(null);
    const fileSnRef = useRef<number>(null);
    const activeImage = useRef<HTMLImageElement | null>(null);

    useEffect(() => {
      const editor = quillRef.current?.getEditor();
      if (!editor) return;

      if (defaultValue) {
        editor.clipboard.dangerouslyPasteHTML(defaultValue);
      }
      if (defaultImage) {
        const { fileId, fileSn } = defaultImage;
        fileIdRef.current = fileId;
        fileSnRef.current = fileSn!;

        const imageInEditor = Array.from(
          editor.container.querySelectorAll("img")
        ).find((img) => getFileSnFromFileUrl(img.src) === fileSn);

        if (imageInEditor) {
          activeImage.current = imageInEditor;
          activeImage.current.style.border = `2px solid ${theme.main[4]}`;
        }
      }
      attachImageClickHandlers();

      // Detect Ctrl+Z / Cmd+Z for rollback
      const handleUndoRedo = (event: KeyboardEvent) => {
        if ((event.ctrlKey || event.metaKey) && event.key === "z") {
          setTimeout(() => {
            detectRemovedImages();
            attachImageClickHandlers();
          }, 100); // Allow undo to process first
        }
      };

      document.addEventListener("keydown", handleUndoRedo);

      return () => {
        document.removeEventListener("keydown", handleUndoRedo);
      };
    }, [defaultValue]);

    /** Attach click event listener to all images in editor */
    const attachImageClickHandlers = () => {
      const editor = quillRef.current?.getEditor();
      if (!editor) return;

      editor.container.querySelectorAll("img").forEach((img) => {
        img.style.cursor = "pointer";
        img.removeEventListener("click", handleImageClick);
        img.addEventListener("click", handleImageClick);
      });
    };

    /** Detects removed images and clears fileRef */
    const detectRemovedImages = () => {
      if (!quillRef.current) return;

      const editor = quillRef.current.getEditor();
      const imagesInEditor = Array.from(
        editor.container.querySelectorAll("img")
      );
      const imageFileSnList = imagesInEditor.map((img) =>
        getFileSnFromFileUrl(img.src)
      );

      if (
        !fileSnRef.current ||
        (fileSnRef.current && !imageFileSnList.includes(fileSnRef.current))
      ) {
        if (imageFileSnList.length > 0) {
          fileSnRef.current = imageFileSnList[0]!;
          activeImage.current = imagesInEditor[0];
          imagesInEditor.forEach((img) => (img.style.border = "none"));
          activeImage.current.style.border = `2px solid ${theme.main[4]}`;
        } else {
          fileSnRef.current = null;
        }
      }
    };

    /** Handles image click and updates fileRef */
    const handleImageClick = (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      const target = event.target as HTMLImageElement;
      const fileSn = getFileSnFromFileUrl(target.src);
      if (!fileSn) return;

      if (activeImage.current) {
        activeImage.current.style.border = "none";
      }

      target.style.border = `2px solid ${theme.main[4]}`;
      activeImage.current = target;

      if (fileSn) {
        fileSnRef.current = fileSn;
      }
    };

    const imageHandler = () => {
      const input: HTMLInputElement = document.createElement("input");
      input.setAttribute("type", "file");
      input.setAttribute("accept", "image/*");
      input.click();

      input.addEventListener("change", async () => {
        if (quillRef.current && input.files && input.files.length > 0) {
          const inputFile = input.files?.[0];

          try {
            const res = await uploadFiles([inputFile], fileIdRef.current);
            const [file] = res.files;
            const imgUrl = getFileUrl(file);

            const editor = quillRef.current.getEditor();
            const range: any = editor.getSelection();
            const imgTag = `<img src="${imgUrl}" alt="${inputFile.name}" />`;

            editor.clipboard.dangerouslyPasteHTML(range.index, imgTag);
            editor.setSelection(range.index + 1);

            setTimeout(() => {
              attachImageClickHandlers();
            }, 100);

            if (!fileIdRef.current) {
              editor.container.querySelectorAll("img").forEach((img) => {
                img.style.border = `2px solid ${theme.main[4]}`;
                activeImage.current = img;
              });
              fileIdRef.current = file.fileId;
              fileSnRef.current = file.fileSn!;
            }
          } catch (error) {
            console.error(error);
            alert("오류가 발생하였습니다.");
          }
        }
      });
    };

    const modules = useMemo(
      () => ({
        toolbar: {
          container: "#toolbar",
          handlers: {
            image: imageHandler,
          },
        },
        clipboard: {
          matchVisual: false,
        },
      }),
      []
    );

    useImperativeHandle(ref, () => ({
      getContents: () => {
        if (!quillRef.current) return "";

        const editor = quillRef.current.getEditor();
        const container = editor.container.cloneNode(true) as HTMLElement; // Clone content

        // Remove only `border` and `cursor` styles from images
        container.querySelectorAll("img").forEach((img) => {
          img.style.border = "";
          img.style.cursor = "";
        });

        return container.querySelector(".ql-editor")?.getHTML(); // Return modified HTML
      },
      getFile: () =>
        fileSnRef.current
          ? ({
              fileId: fileIdRef.current,
              fileSn: fileSnRef.current,
            } as FileType)
          : undefined,
      setContents: (content: string) => {
        if (quillRef.current) {
          const editor = quillRef.current.getEditor();
          editor.clipboard.dangerouslyPasteHTML(content);
        }
      },
    }));

    return (
      <div className="bg-white">
        <CustomToolbar />
        <ReactQuill
          ref={quillRef}
          theme="snow"
          modules={modules}
          formats={formats}
          onChange={(content) => {
            detectRemovedImages(); // Detect deleted images
            onChange(content);
          }}
          defaultValue={defaultValue}
          style={{ height: "300px" }}
          {...rest}
        />
      </div>
    );
  }
);

_TextEditor.displayName = "TextEditor";
export default _TextEditor;
