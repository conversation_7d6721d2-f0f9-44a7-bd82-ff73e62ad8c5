import { theme } from "@/lib/theme";
import { useEffect, useRef } from "react";
import styled from "styled-components";
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  $padding?: string;
  $maxWidth?: string;
  $fontSize?: string;
}

const WhiteInput_ = styled.input<{
  $padding?: string;
  $maxWidth?: string;
  $fontSize?: string;
}>`
  width: 100%;
  max-width: ${(props) => props.$maxWidth || `100%`};
  flex: 1;
  min-width: 0;
  padding: ${(props) => props.$padding || `10px`};
  font-size: ${(props) => props.$fontSize || `16px`};
  caret-color: black;
  background-color: transparent;
  // color: ${theme.black[0]};

  outline: none;
  &:focus {
    // outline: none;
  }
`;
const WhiteInput: React.FC<InputProps> = ({ defaultValue, ...props }) => {
  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (ref && ref.current) {
      ref.current.value = defaultValue as any;
    }
  }, [defaultValue]);
  return <WhiteInput_ ref={ref} {...props} />;
};

export default WhiteInput;
