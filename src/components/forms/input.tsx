"use client";
import {
  forwardRef,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
} from "react";
import styled from "styled-components";
import { theme } from "@/lib/theme";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  margin?: string;
  padding?: string;
  $width?: string;
}

const InputWrapper = styled.div<{ margin?: string; $width?: string }>`
  position: relative;
  display: flex;
  margin: ${(props) => props.margin || "10px 0"};
  width: ${({ $width }) => $width || "auto"};
`;

const StyledInput = styled.input<{ padding?: string }>`
  width: 100%;
  flex: 1;
  min-width: 0;
  padding: ${(props) => props.padding || `10px`};
  padding-top: 20px;
  height: 50px;
  border: 1px solid ${theme.black[6]};
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 16px;
  caret-color: black;
  color: ${theme.black[0]};

  &:focus {
    outline: none;
    border-color: ${theme.main[4]};
  }
`;

const Label = styled.label<{ $active: boolean }>`
  position: absolute;
  left: 10px;
  top: ${(props) => (props.$active ? "4px" : "50%")};
  transform: ${(props) => (props.$active ? "none" : "translateY(-50%)")};
  font-size: ${(props) => (props.$active ? "12px" : "14px")};
  color: ${theme.black[5]};
  transition: all 0.3s ease-in-out;
  pointer-events: none;
`;

const Input = forwardRef<InputRef, InputProps>(
  ({ label, margin, padding, $width, className, ...props }, ref) => {
    const [isFocused, setIsFocused] = useState(true);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
      if (props.value) {
        setIsFocused(true);
      }
    }, [props.value]);

    const handleBlur = () => {
      if (inputRef.current && !inputRef.current.value) {
        setIsFocused(false);
      }
    };

    useImperativeHandle(ref, () => ({
      focus: () => inputRef.current?.focus(),
      clear: () => {
        if (inputRef.current) {
          inputRef.current.value = "";
          setIsFocused(false);
        }
      },
    }));

    const active = isFocused || !!props.value;

    return (
      <InputWrapper className={className} margin={margin} $width={$width}>
        <Label $active={active}>{label}</Label>
        <StyledInput
          ref={inputRef}
          padding={padding}
          onFocus={() => setIsFocused(true)}
          onBlur={handleBlur}
          {...props}
        />
      </InputWrapper>
    );
  }
);

Input.displayName = "Input";

export default Input;
