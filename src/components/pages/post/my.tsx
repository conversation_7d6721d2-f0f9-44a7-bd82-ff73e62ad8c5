"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  Col,
  CustomText,
  ImageWrapper,
  Row,
  TabButton,
  TabsContainer,
  Title,
} from "@/components/extra/Common";
import Button from "@/components/forms/button";
import { formatCompactNumber } from "@/lib/number";
import { theme } from "@/lib/theme";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Link, useRouter } from "@/i18n/routing";
import {
  deletePost,
  getPostCategories,
  getPosts,
} from "@/services/postService";
import { getFileUrl } from "@/lib/file";
import { timeAgo } from "@/lib/date";
import { FRONT_URL_PATH } from "@/lib/constants";
import { useUserStore } from "@/store/userStore";
import NoData from "@/components/ui/no-data";
import Pager from "@/components/ui/pager";
import { stripHtmlTags } from "@/lib/text";
import { PencilIcon } from "@/components/ui/icons";

type MyPostDataType = {
  title: string;
  content: string;
  category: string;
  createdAt: string;
  authorName: string;
  thumbnailId?: string;
  thumbnailSn?: number;
};

interface IMyPostProps {
  data: MyPostDataType[];
}

const MyPost: React.FC = ({}) => {
  const t = useTranslations("Posts");
  const locale = useLocale();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState({ page: 0, size: 10, totalCount: 0 });

  const [data, setData] = useState<PostListType[]>([]);
  const [filteredData, setFilteredData] = useState<PostListType[]>([]);
  const [selectOptions, setSelectOptions] = useState<LabelValueType[]>([
    { value: "", label: "전체 보기" },
  ]);
  const [selectedTab, setSelectedTab] = useState(selectOptions[0].value);
  const _prepared = useUserStore((state) => state._prepared);
  const user = useUserStore((state) => state.user);

  const init = useCallback(async () => {
    if (!user?.userId) return;
    setLoading(true);

    const list = await getPostCategories({ locale });
    setSelectOptions([...selectOptions, ...list.items]);
    const body = {
      ...query,
      locale,
      title: "",
      postCategoryId: "",
      athrId: user.userId,
    };

    const { items, totalCount } = await getPosts(body);

    setData(items);
    setFilteredData(items);
    setQuery((prev) => ({ ...prev, totalCount }));
    setLoading(false);
  }, [user?.userId, locale, query.page]);

  useEffect(() => {
    if (user?.userId && _prepared) {
      init();
    }
  }, [user?.userId, _prepared, query.page]);

  useEffect(() => {
    if (selectedTab) {
      setFilteredData(
        data.filter((item) => {
          return item.postCategoryId === selectedTab;
        })
      );
    } else {
      setFilteredData(data);
    }
  }, [selectedTab]);

  const handleDelete = async (id: number) => {
    const confirm = window.confirm("게시글을 삭제 하시겠습니까?");
    if (confirm) {
      try {
        await deletePost(id);
        alert("게시글이 삭제 되었습니다.");
        if (!user?.userId) return;
        init();
      } catch (e) {
        alert("삭제중 오류가 발생하였습니다.");
      }
    }
  };

  const handleUpdate = async (postId: number) => {
    const confirm = window.confirm("게시글을 수정 하시겠습니까?");
    if (confirm) {
      router.push(`/posts/form/${postId}`);
    }
  };

  const pagerProps: IPager = {
    ...query,
    onPageChange: (page) => setQuery((prev) => ({ ...prev, page })),
  };

  return (
    user &&
    user.userId &&
    !loading && (
      <>
        <div className="flex justify-between p-3 md:p-0">
          {/* 버튼 */}
          <div></div>
          <div className="flex gap-3">
            {/* 검색하기 주석 */}
            {/* <Button
                className="h-fit inline-flex gap-2 items-center"
                $fontSize="14px"
                $theme="gray"
                $padding="8px 10px"
              >
                <SearchIcon />
                <span>{t("search-label")}</span>
              </Button> */}
            <Button
              className="h-fit inline-flex gap-2 items-center"
              $fontSize="14px"
              $padding="8px 10px"
              onClick={() => router.push(`${FRONT_URL_PATH.POSTS}/form`)}
            >
              <PencilIcon />
              <span>{t("create-label")}</span>
            </Button>
          </div>
        </div>
        <CardContainer>
          {filteredData.length > 0 &&
            filteredData.map((item, idx) => {
              const {
                postId,
                authorName,
                createdAt,
                title,
                content,
                thumbnailId,
                thumbnailNum,
                userProfileImage,
                likeCnt,
                viewCnt,
                commentCnt,
              } = item;

              return (
                <CardWrapper key={`MyPost_${idx}`} $margin="0" $height="auto">
                  <ProfileSection
                    profileSrc={
                      userProfileImage?.fileId &&
                      userProfileImage.fileSn !== undefined
                        ? getFileUrl({
                            fileId: userProfileImage?.fileId,
                            fileSn: userProfileImage?.fileSn,
                          })
                        : "/images/example/guest.png"
                    }
                    authorName={authorName}
                    createdAt={createdAt}
                    postId={postId}
                    handleDelete={handleDelete}
                    handleUpdate={handleUpdate}
                  />
                  <Link
                    href={`${FRONT_URL_PATH.POSTS}/${postId}`}
                    style={{ cursor: "pointer" }}
                  >
                    <BoardSection
                      title={title}
                      content={stripHtmlTags(content)}
                    />
                    <Thumbnail
                      thumbnailSrc={
                        thumbnailId && thumbnailNum !== undefined
                          ? getFileUrl({
                              fileId: thumbnailId,
                              fileSn: thumbnailNum,
                            })
                          : "/images/example/mountain.png"
                      }
                    />
                    <InfoSection
                      likeCnt={formatCompactNumber(likeCnt)}
                      commentCnt={formatCompactNumber(commentCnt)}
                      viewCnt={formatCompactNumber(viewCnt)}
                    />
                  </Link>
                </CardWrapper>
              );
            })}
        </CardContainer>
        {filteredData.length > 0 ? <Pager {...pagerProps} /> : <NoData />}
      </>
    )
  );
};

export default MyPost;

const ProfileSection = ({
  profileSrc,
  authorName,
  createdAt,
  postId,
  handleDelete,
  handleUpdate,
}: {
  profileSrc: string;
  authorName: string;
  createdAt: string;
  postId: number;
  handleDelete: Function;
  handleUpdate: Function;
}) => (
  <Row>
    <Col $width="120px">
      <ImageWrapper $ratio="1/1" $radius="50%" style={{ overflow: "hidden" }}>
        <img src={profileSrc} alt="profile_01" style={{ objectFit: "cover" }} />
      </ImageWrapper>
    </Col>
    <Col $margin="auto 0 auto 10px">
      <CustomText $fontWeight="600">{authorName}</CustomText>
      <CustomText $color={theme.black[4]}>{timeAgo(createdAt)}</CustomText>
    </Col>
    <Col $align="-webkit-right">
      <Row style={{ justifyContent: "right" }}>
        <CustomText
          $color={theme.mute[2]}
          $margin="0 5px"
          $cursor="pointer"
          onClick={() => handleUpdate(postId)}
        >
          수정
        </CustomText>
        <CustomText
          $color={theme.mute[2]}
          $margin="0 10px 0 5px"
          $cursor="pointer"
          onClick={() => handleDelete(postId)}
        >
          삭제
        </CustomText>
        <ImageWrapper $width="15px" $height="30px" $cursor="pointer">
          <Image src={`/icons/more.png`} alt="more" width={14} height={28} />
        </ImageWrapper>
      </Row>
    </Col>
  </Row>
);

const BoardSection = ({
  title,
  content,
}: {
  title: string;
  content: string;
}) => (
  <Col
    $margin="20px 0"
    $cursor="pointer"
    $height="60px"
    style={{ cursor: "pointer" }}
  >
    <CustomText $fontSize="16px" $fontWeight="600">
      {title}
    </CustomText>
    <CustomText $maxlines={3} dangerouslySetInnerHTML={{ __html: content }} />
  </Col>
);

const Thumbnail = ({ thumbnailSrc }: { thumbnailSrc: string }) => (
  <ImageWrapper
    style={{ width: "100%", height: "auto", position: "relative" }}
    $cursor="pointer"
  >
    <img
      src={thumbnailSrc}
      alt="thumbnail"
      width={0} // 자동 조정
      height={0} // 자동 조정
      sizes="100vw"
      style={{ width: "100%", height: "auto", objectFit: "cover" }}
    />
  </ImageWrapper>
);

const InfoSection = ({
  likeCnt,
  commentCnt,
  viewCnt,
}: {
  likeCnt: string;
  commentCnt: string;
  viewCnt: string;
}) => (
  <Row $margin="20px 0 0 0">
    <ImageWrapper $width="24px" $height="20px">
      <Image src={"/icons/like.png"} alt="like" width={14} height={12} />
    </ImageWrapper>
    <CustomText $fontSize="12px" $padding="2px 0 0 0" $margin="0 8px 0 0">
      {likeCnt}
    </CustomText>
    <ImageWrapper $width="24px" $height="20px">
      <Image src={"/icons/comment.png"} alt="comment" width={14} height={12} />
    </ImageWrapper>
    <CustomText $fontSize="12px" $padding="2px 0 0 0" $margin="0 8px 0 0">
      {commentCnt}
    </CustomText>
    <ImageWrapper $width="24px" $height="20px">
      <Image src={"/icons/view.png"} alt="view" width={14} height={12} />
    </ImageWrapper>
    <CustomText $fontSize="12px" $padding="2px 0 0 0" $margin="0 8px 0 0">
      {viewCnt}
    </CustomText>
  </Row>
);
