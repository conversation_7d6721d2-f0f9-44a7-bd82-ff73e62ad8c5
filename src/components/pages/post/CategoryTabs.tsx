"use client";
import { Link } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

interface ICategoryTabs {
  items: LabelValueType[];
}

export const CategoryTabs = ({ items }: ICategoryTabs) => {
  const t = useTranslations("Posts");
  const params = useSearchParams();

  const baseOption = { value: "", label: t("see-all") };

  const categoryOptions = useMemo(() => [{ ...baseOption }, ...items], []);

  const [category, setCategory] = useState(
    params.get("c") || categoryOptions[0].value
  );
  useEffect(() => {
    if (categoryOptions.find((co) => co.value === params.get("c"))) {
      setCategory(params.get("c"));
    } else {
      setCategory(baseOption.value);
    }
  }, [params.get("c")]);

  return (
    <div className="flex md:flex-col gap-4 border-gray-300 overflow-x-auto px-4 py-2 md:py-4 bg-white md:rounded-md">
      {categoryOptions.map((cate, idx) => (
        <Link
          key={`POST_CATEGORY_${idx}`}
          href={`${FRONT_URL_PATH.POSTS}?c=${cate.value}`}
          className={`text-sm md:text-base whitespace-nowrap pb-1 border-b-2 md:border-none transition-all duration-150 ${
            category === cate.value
              ? "font-bold border-black"
              : "font-normal border-transparent md:hover:border-black"
          }`}
        >
          {cate.label}
        </Link>
      ))}
    </div>
  );
};
