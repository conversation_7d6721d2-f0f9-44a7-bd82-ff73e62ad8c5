"use client";
import { Title } from "@/components/extra/Common";
import { useTranslations } from "next-intl";
import { getFileUrl } from "@/lib/file";
import Comment from "./Comment";
import { format } from "date-fns";
import { CommentIcon, ThumbsUpIcon, UserCircle } from "@/components/ui/icons";
import ProfileImage from "@/components/ui/ProfileImage";
import { formatNumber } from "@/lib/number";
import Button from "@/components/forms/button";
import { toggleLike } from "@/services/postService";
import { useMemo, useState } from "react";

interface IPostItem {
  item: PostItemType;
}
const PostItem = ({ item }: IPostItem) => {
  const t = useTranslations("Posts");
  const [likeYn, setLikeYn] = useState<boolean>(item.likeYn);
  const onClickLike = async () => {
    const like = await toggleLike(item.postId);
    setLikeYn(like);
  };
  const getContent = () =>
    useMemo(
      () => <div dangerouslySetInnerHTML={{ __html: item.content }}></div>,
      [item.content]
    );
  return (
    <div className="bg-white md:rounded-md md:p-3 w-full md:min-h-[600px] flex flex-col justify-between">
      <div className="p-2">
        <Title>
          <span>[{item.category}]</span> {item.title}
        </Title>
        <hr className="my-3" />
        <div className="flex gap-2 items-center text-gray-600 text-sm">
          <ProfileImage size={40} {...item.userProfileImage} />
          <div className="flex flex-col">
            <span>{item.authorName}</span>
            <div className="flex items-center gap-1">
              {/* 작성일 */}
              <span>{format(item.createdAt, "P")}</span>
              <span className="relative before:content-['·'] before:text-gray-400"></span>
              {/* 추천수 */}
              <div className="flex items-center gap-1">
                <ThumbsUpIcon />
                <span>{formatNumber(item.likeCnt)}</span>
              </div>
              <span className="relative before:content-['·'] before:text-gray-400"></span>
              <div className="flex items-center gap-1">
                <CommentIcon />
                <span>{formatNumber(item.commentCnt)}</span>
              </div>
            </div>
          </div>
        </div>
        <hr className="my-3" />
      </div>
      <aside className="p-2">
        {getContent()}
        {/* <div dangerouslySetInnerHTML={{ __html: item.content }}></div> */}
      </aside>
      <div className="flex justify-center py-2">
        <button
          className="flex items-center gap-1 px-4 py-2 rounded-xl border text-sm font-semibold text-gray-900 border-gray-200"
          onClick={onClickLike}
        >
          <ThumbsUpIcon fill={likeYn ? "#4EEA7C" : "#B4B4B4"} />
          {t("like-label")}
        </button>
      </div>
      <Comment postId={item.postId} />
    </div>
  );
};

export default PostItem;
