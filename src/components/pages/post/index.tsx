"use client";

import Button from "@/components/forms/button";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Link, useRouter } from "@/i18n/routing";
import { getPosts } from "@/services/postService";
import { getFileUrl } from "@/lib/file";
import { timeAgo } from "@/lib/date";
import { FRONT_URL_PATH } from "@/lib/constants";
import Pager from "@/components/ui/pager";
import NoData from "@/components/ui/no-data";
import { PencilIcon, SearchIcon, UserCircle } from "@/components/ui/icons";
import { useSearchParams } from "next/navigation";
import { useUserStore } from "@/store/userStore";

interface IPostListQuery {
  page: number;
  size: number;
  postCategoryId?: string;
}
const Post = () => {
  const t = useTranslations("Posts");
  const locale = useLocale();
  const router = useRouter();
  const params = useSearchParams();

  const user = useUserStore((state) => state.user);

  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState<IPostListQuery>({
    page: 0,
    size: 10,
    postCategoryId: undefined,
  });
  const [items, setItems] = useState<PostListType[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const postCategoryId = params.get("c") || undefined;

  useEffect(() => {
    setQuery((prev) => ({
      ...prev,
      postCategoryId,
    }));
  }, [postCategoryId]);

  const init = useCallback(async () => {
    setLoading(true);
    const body = {
      ...query,
      locale,
      title: "",
    };
    const { items, totalCount }: { items: PostListType[]; totalCount: number } =
      await getPosts(body);
    setItems(items);
    setTotalCount(totalCount);
    setLoading(false);
  }, [query]);

  useEffect(() => {
    init();
  }, [query]);

  const pagerProps: IPager = {
    ...query,
    totalCount,
    onPageChange: (page) => setQuery((prev) => ({ ...prev, page })),
  };

  if (loading) {
    return null;
  }

  return (
    <div className="bg-white md:rounded-md md:p-3 w-full md:min-h-[600px] flex flex-col justify-between">
      <div>
        {/* 메인 */}
        <div className="flex justify-between p-3 md:p-0">
          {/* 버튼 */}
          <div></div>
          <div className="flex gap-3">
            {/* 검색하기 주석 */}
            {/* <Button
                className="h-fit inline-flex gap-2 items-center"
                $fontSize="14px"
                $theme="gray"
                $padding="8px 10px"
              >
                <SearchIcon />
                <span>{t("search-label")}</span>
              </Button> */}
            <Button
              className="h-fit inline-flex gap-2 items-center"
              $fontSize="14px"
              $padding="8px 10px"
              onClick={() =>
                router.push(
                  !user ? FRONT_URL_PATH.SIGNIN : `${FRONT_URL_PATH.POSTS}/form`
                )
              }
            >
              <PencilIcon />
              <span>{t("create-label")}</span>
            </Button>
          </div>
        </div>
        {!loading && items.length === 0 ? (
          <NoData />
        ) : (
          <ul>
            {items.map((item, itemKey) => (
              <li key={`POST_ITEM_${itemKey}`}>
                <ListItem {...item} />
              </li>
            ))}
          </ul>
        )}
      </div>
      <div>
        {/* Pager */}
        <Pager {...pagerProps} />
      </div>
    </div>
  );
};

export default Post;

const ListItem = ({
  postId,
  title,
  authorName,
  createdAt,
  userProfileImage,
  thumbnailId,
  thumbnailNum,
  likeCnt,
  commentCnt,
  viewCnt,
}: PostListType) => {
  return (
    <div className="flex flex-col gap-4 border-b p-2">
      <Link
        href={`${FRONT_URL_PATH.POSTS}/${postId}`}
        className="flex flex-row gap-4"
      >
        <div className="flex-1">
          <h2 className="text-base font-semibold line-clamp-2 break-all">
            {title}
          </h2>

          <div className="flex items-center gap-2 mt-2">
            <div className="w-6 h-6 rounded-full overflow-hidden">
              {userProfileImage ? (
                <img
                  style={{ borderRadius: "50%", aspectRatio: 1 }}
                  src={getFileUrl(userProfileImage)}
                  width={25}
                  height={25}
                  alt="author-image"
                />
              ) : (
                <UserCircle className="text-2xl text-gray-600" />
              )}
            </div>
            <div className="text-sm font-medium">{authorName}</div>
          </div>

          <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
            <div className="text-xs text-gray-400 whitespace-nowrap">
              {timeAgo(createdAt)}
            </div>
            <div className="flex items-center gap-1">
              <Image src="/icons/like.png" alt="like" width={14} height={12} />
              <span>{likeCnt}</span>
            </div>
            <div className="flex items-center gap-1">
              <Image
                src="/icons/comment.png"
                alt="comment"
                width={14}
                height={12}
              />
              <span>{commentCnt}</span>
            </div>
            <div className="flex items-center gap-1">
              <Image src="/icons/view.png" alt="view" width={14} height={12} />
              <span>{viewCnt}</span>
            </div>
          </div>
        </div>
        {thumbnailId && (
          <div className="w-20 h-20 relative rounded-md overflow-hidden">
            <img
              src={getFileUrl({ fileId: thumbnailId, fileSn: thumbnailNum })}
              alt="thumbnail"
              className="absolute w-full h-full object-cover"
            />
          </div>
        )}
      </Link>
    </div>
  );
};
