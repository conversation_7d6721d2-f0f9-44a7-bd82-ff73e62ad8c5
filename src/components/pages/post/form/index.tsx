"use client";

import { Between, Col, Row, Title } from "@/components/extra/Common";
import Button from "@/components/forms/button";
import Input from "@/components/forms/input";
import TextEditor from "@/components/forms/text-editor";
import Select from "@/components/ui/Select/select";
import {
  createPost,
  getPostCategories,
  getPosts,
  updatePost,
} from "@/services/postService";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "@/i18n/routing";
import { useCallback, useEffect, useRef, useState } from "react";
import { FRONT_URL_PATH } from "@/lib/constants";
import { useUserStore } from "@/store/userStore";
import { useAuthGuard } from "@/hooks/useAuthGuard";
import NoData from "@/components/ui/no-data";

interface IReviewFormProps {
  initialData?: PostItemType;
}

const ReviewForm: React.FC<IReviewFormProps> = ({ initialData }) => {
  const isAuthenticated = useAuthGuard();

  const [editorLoaded, setEditorLoaded] = useState(false);
  const [param, setParam] = useState({
    title: "",
    postCategoryId: "",
    postId: 0,
  });
  const [selectOptions, setSelectOptions] = useState<LabelValueType[]>([
    { label: "선택", value: "" },
  ]);
  const t = useTranslations("Review");
  const tu = useTranslations("Auth");
  const textRef = useRef<TextEditorRef>(null);
  const locale = useLocale();
  const router = useRouter();

  const init = useCallback(async () => {
    const { items, totalCount } = await getPostCategories({ locale });
    setSelectOptions([...selectOptions, ...items]);

    if (initialData) {
      setParam({ ...initialData });
    }

    if (initialData?.content && textRef.current) {
      textRef.current.setContents(initialData.content || ""); // 에디터에 값 설정
    }
  }, []);

  const handleSave = async () => {
    const content = textRef.current?.getContents();
    const thumbnail = textRef.current?.getFile();

    // 유효성 검사
    if (!param.title.trim()) {
      return alert("제목을 입력해주세요.");
    }
    if (!content?.trim()) {
      return alert("내용을 입력해주세요.");
    }
    if (!param.postCategoryId) {
      return alert("카테고리를 선택해주세요.");
    }

    // 게시글 저장 여부 확인
    const isConfirmed = confirm("게시글을 저장하시겠습니까?");
    if (!isConfirmed) return;

    const body: CreatePostType = {
      title: param.title,
      postCategoryId: param.postCategoryId,
      content,
      thumbnailId: thumbnail?.fileId,
      thumbnailNum: thumbnail?.fileSn,
    };

    try {
      if (param.postId) {
        body.postId = param.postId;
        await updatePost(body);
        alert("게시글 수정이 완료되었습니다.");
        router.push(FRONT_URL_PATH.POSTS_MY);
      } else {
        await createPost(body); // 게시글 생성 API 호출
        alert("게시글 등록이 완료되었습니다.");
        router.push(FRONT_URL_PATH.POSTS);
      }
    } catch (error) {
      alert("게시글 저장 중 오류가 발생했습니다.");
    }
  };

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    if (textRef.current) {
      setEditorLoaded(true);
    }
  }, [textRef.current]);

  useEffect(() => {
    if (editorLoaded && initialData?.content) {
      textRef.current?.setContents(initialData.content || "");
    }
  }, [editorLoaded, initialData]);

  if (!isAuthenticated) {
    return <NoData />;
  }
  return (
    <>
      <Between>
        <Title>{t("title")}</Title>
        <Button $padding="10px 20px" onClick={() => handleSave()}>
          {param?.postId ? `수정하기` : `등록하기`}
        </Button>
      </Between>
      <Col>
        <Row $gap="10px">
          <Input
            label={"제목"}
            $width="inherit"
            value={param.title}
            onChange={(e) =>
              setParam((prev) => ({ ...prev, title: e.target.value }))
            }
          />
          <Select
            options={selectOptions}
            defaultValue={param.postCategoryId || ""}
            onChange={(e) =>
              setParam((prev) => ({ ...prev, postCategoryId: e }))
            }
            $width="150px"
          />
        </Row>
        <TextEditor ref={textRef} />
      </Col>
    </>
  );
};

export default ReviewForm;
