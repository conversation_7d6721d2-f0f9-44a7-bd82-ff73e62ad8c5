"use client";

import { useState, useEffect, useCallback } from "react";
import { CustomText } from "@/components/extra/Common";
import { useUserStore } from "@/store/userStore";
import Button from "../../forms/button";
import {
  createComment,
  deleteComment,
  getCommentsByPostId,
  toggleCommentLike,
} from "@/services/commentService";
import styled from "styled-components";
import { theme } from "@/lib/theme";
import { timeAgo } from "@/lib/date";
import { useTranslations } from "next-intl";
import Textarea from "@/components/forms/textarea";
import ProfileImage from "@/components/ui/ProfileImage";
import { CommentIcon, ThumbsUpIcon } from "@/components/ui/icons";
import { formatNumber } from "@/lib/number";
import { useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";

export default function Comment({ postId }: { postId: number }) {
  const t = useTranslations("Posts.Comment");
  const { user } = useUserStore();
  const router = useRouter();
  const [comments, setComments] = useState<CommentType[]>([]);
  const [totalCnt, setTotalCnt] = useState<number>(0);
  const [newComment, setNewComment] = useState("");
  const [replyComment, setReplyComment] = useState({
    upperCommentId: 0,
    cntnt: "",
  });

  const onClickCommentLike = async ({
    commentId,
    upperCommentId,
  }: {
    commentId: number;
    upperCommentId?: number;
  }) => {
    if (!user) {
      router.push(FRONT_URL_PATH.SIGNIN);
    }
    await toggleCommentLike(commentId);
    init();
  };
  const init = useCallback(async () => {
    try {
      const { items, totalCount } = await getCommentsByPostId(postId);
      setComments(items);
      setTotalCnt(totalCount);
    } catch (e) {
      console.error(e);
      alert("오류가 발생하였습니다.");
    }
  }, []);

  useEffect(() => {
    init();
  }, [postId]);

  const handleAddComment = async () => {
    if (!user) {
      return alert("로그인 이후 댓글 등록이 가능합니다.");
    }

    try {
      const confirm = window.confirm("댓글을 등록하시겠습니까?");
      if (confirm) {
        const body: CreateCommentRequest = {
          postId,
          usrId: user?.userId || 0,
          cntnt: newComment,
        };
        await createComment(body);
        setNewComment("");
        await init();
        alert("댓글이 등록되었습니다.");
      }
    } catch (e) {
      console.error(e);
      alert("오류가 발생하였습니다.");
    }
  };

  const handleAddReply = async () => {
    if (!user) {
      return alert("로그인 이후 답글 등록이 가능합니다.");
    }

    try {
      const confirm = window.confirm("답글을 등록하시겠습니까?");
      if (confirm) {
        const body: CreateCommentRequest = {
          usrId: user?.userId || 0,
          cntnt: replyComment.cntnt,
          upperCommentId: replyComment.upperCommentId,
        };
        await createComment(body);
        setReplyComment({ upperCommentId: 0, cntnt: "" });
        await init();
        alert("답글이 등록되었습니다.");
      }
    } catch (e) {
      console.error(e);
      alert("오류가 발생하였습니다.");
    }
  };

  const handleReplyTextArea = async (comment: CommentType) => {
    if (comment.commentId == replyComment.upperCommentId) {
      setReplyComment({ upperCommentId: 0, cntnt: "" });
    } else {
      setReplyComment((prev) => ({
        ...prev,
        upperCommentId: comment.commentId,
      }));
    }
  };

  const handleDeleteComment = async (comment: CommentType) => {
    if (!user || !user.userId) {
      return alert("권한이 없습니다.");
    }

    const confirm = window.confirm("댓글을 삭제 하시겠습니까?");

    if (confirm) {
      const body: DeleteCommentRequest = {
        commentId: comment.commentId,
      };

      try {
        await deleteComment(body);
        await init();
        alert("댓글이 삭제되었습니다.");
      } catch (e) {
        console.error(e);
        alert("오류가 발생하였습니다.");
      }
    }
  };

  return (
    <div className="p-2 md:p-0">
      <CommentHeader>
        <CustomText $fontSize="16px" $fontWeight="bold">
          {t("comment-count", { n: totalCnt })}
        </CustomText>
      </CommentHeader>

      <div className="flex flex-col gap-2 my-[20px] border border-[#DADADA] rounded-[10px] p-[10px]">
        <div className="flex gap-1 items-center">
          <ProfileImage size={33} {...user?.image} />
          <span className="font-bold">{user?.name || ""}</span>
        </div>
        <Textarea
          className="border-none"
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder={t("enter-comment-label")}
        />
        <div className="flex justify-end">
          <Button
            className="h-fit"
            $fontSize="14px"
            $padding="10px 14px"
            onClick={handleAddComment}
          >
            {t("post-comment-label")}
          </Button>
        </div>
      </div>

      <CommentList>
        {comments.map((comment, commentIdx) => (
          <div key={`COMMENT_${commentIdx}`}>
            {/* 댓글 Start */}
            <div className="py-2">
              {/* Row 1 Start */}
              <div className="flex justify-between items-start">
                {/* Profile Start */}
                <div className="flex gap-2 items-center">
                  <ProfileImage size={33} {...comment.userProfileImage} />
                  <div className="flex flex-col">
                    <span className="font-bold">{comment.name}</span>
                    <span className="text-sm text-gray-500">
                      {timeAgo(comment.createdAt)}
                    </span>
                  </div>
                </div>
                {/* Profile End */}

                <div className="flex h-fit">
                  {comment.userId === user?.userId && (
                    <>
                      <SmallButton onClick={() => handleDeleteComment(comment)}>
                        {t("delete-label")}
                      </SmallButton>
                    </>
                  )}
                </div>
              </div>
              {/* Row 1 End */}
              {/* Row 2 Start */}
              <p className="py-2">{comment.content}</p>
              {/* Row 2 End */}
              {/* Row 3 Start */}
              <div className="flex items-center gap-1 text-gray-500 text-sm">
                <div
                  className="flex items-center gap-1 cursor-pointer"
                  onClick={() =>
                    onClickCommentLike({ commentId: comment.commentId })
                  }
                >
                  <ThumbsUpIcon fill={comment.likeYn ? "#4EEA7C" : "#B4B4B4"} />
                  <span>{formatNumber(comment.likeCnt)}</span>
                </div>
                <span className="relative before:content-['·'] before:text-gray-500"></span>
                <div className="flex items-center gap-1">
                  <CommentIcon />
                  <span>{formatNumber(comment.commentCnt)}</span>
                </div>
                <span className="relative before:content-['·'] before:text-gray-500"></span>
                <div
                  className="cursor-pointer"
                  onClick={() => handleReplyTextArea(comment)}
                >
                  <span>{t("post-reply-label")}</span>
                </div>
              </div>
              {/* Row 3 End */}
            </div>
            {/* 댓글 End */}
            {/* 댓글에 답글 Start */}
            {comment.replies?.map((reply, replyIdx) => {
              return (
                <div
                  key={`REPLY_${commentIdx}_${replyIdx}`}
                  className="ml-[20px] py-2"
                >
                  {/* Row 1 Start */}
                  <div className="flex justify-between">
                    {/* Profile Start */}
                    <div className="flex gap-2 items-center">
                      <ProfileImage size={33} {...reply.userProfileImage} />
                      <div className="flex flex-col">
                        <span className="font-bold">{reply.name}</span>
                        <span className="text-sm text-gray-500">
                          {timeAgo(reply.createdAt)}
                        </span>
                      </div>
                    </div>
                    {/* Profile End */}
                    <div className="flex h-fit">
                      {reply.userId === user?.userId && (
                        <SmallButton onClick={() => handleDeleteComment(reply)}>
                          {t("delete-label")}
                        </SmallButton>
                      )}
                    </div>
                  </div>
                  {/* Row 1 End */}
                  {/* Row 2 Start */}
                  <p className="py-2">{reply.content}</p>
                  {/* Row 2 End */}
                  {/* Row 3 Start */}
                  <div className="flex items-center gap-1 text-gray-500 text-sm">
                    <div
                      className="flex items-center gap-1 cursor-pointer"
                      onClick={() =>
                        onClickCommentLike({
                          upperCommentId: comment.commentId,
                          commentId: reply.commentId,
                        })
                      }
                    >
                      <ThumbsUpIcon
                        fill={reply.likeYn ? "#4EEA7C" : "#B4B4B4"}
                      />
                      <span>{formatNumber(reply.likeCnt)}</span>
                    </div>
                  </div>
                  {/* Row 3 End */}
                </div>
              );
            })}

            {comment.commentId === replyComment.upperCommentId && (
              <div className="flex flex-col gap-2 ml-[20px] my-[5px] border border-[#DADADA] rounded-[10px] p-[10px]">
                <div className="flex gap-1 items-center">
                  <ProfileImage size={33} {...user?.image} />
                  <span className="font-bold">{user?.name || ""}</span>
                </div>
                <Textarea
                  value={replyComment.cntnt}
                  onChange={(e) =>
                    setReplyComment((prev) => ({
                      ...prev,
                      cntnt: e.target.value,
                    }))
                  }
                  placeholder={t("enter-reply-label")}
                />
                <div className="flex justify-end">
                  <Button
                    className="h-fit"
                    $fontSize="14px"
                    $padding="10px 14px"
                    onClick={handleAddReply}
                  >
                    {t("post-reply-label")}
                  </Button>
                </div>
              </div>
            )}
          </div>
        ))}
      </CommentList>
    </div>
  );
}

const CommentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const CommentHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 2px solid ${theme.mute[1]};
`;

const SmallButton = styled.button<{ $background?: string }>`
  background: ${({ $background }) => $background || theme.black[2]};
  color: ${theme.white[0]};
  padding: 5px 10px;
  border-radius: 6px;
  margin-left: 10px;
  cursor: pointer;
  font-size: 14px;
  border: none;
  transition: all 0.3s ease-in-out;
  &:hover {
    background: ${theme.black[0]};
  }
  @media (max-width: 768px) {
    padding: 3px 6px;
    font-size: 12px;
    margin-left: 5px;
  }
`;
