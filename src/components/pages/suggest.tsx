import Button from "@/components/forms/button";
import Textarea from "@/components/forms/textarea";
import { useTranslations } from "next-intl";

const GunHee = () => {
  const t = useTranslations("Suggestion");
  return (
    <main className="bg-[#222a48] p-2 text-center">
      <h2 className="text-gray-100 text-2xl font-bold">{t("title")}</h2>
      <div className="p-3">
        {/* <Textarea className="w-full" label={t("content-label")} /> */}
      </div>
      <div className="p-3">
        {/* <Textarea className="w-full" label={t("content-label")} /> */}
      </div>
      <div className="p-3 flex gap-3">
        <Button className="bg-gray-100 w-full">{t("cancel-label")}</Button>
        <Button className="bg-gray-100 w-full">{t("confirm-label")}</Button>
      </div>
    </main>
  );
};

export default GunHee;
