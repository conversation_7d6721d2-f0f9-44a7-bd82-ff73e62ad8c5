"use client";
import Input from "@/components/forms/input";
import Button from "@/components/forms/button";
import { signIn } from "@/services/authService";
import { useUserStore } from "@/store/userStore";
import { useRef } from "react";
import { Title } from "@/components/extra/Common";
import { useTranslations } from "next-intl";

const SignInForm = ({ title }: { title?: string }) => {
  const t = useTranslations("Auth.Sign-in");
  const body = useRef<SingInRequestType>({ username: "", password: "" });
  const setUser = useUserStore((state) => state.setUser);

  const onSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const user = await signIn(body.current);
    setUser(user);
  };

  return (
    <form
      onSubmit={onSubmit}
      className="md:min-w-[500px] max-w-md mx-auto bg-white py-3 px-6 rounded-lg space-y-4"
    >
      <Title className="text-2xl font-bold text-center text-gray-800 mb-4">
        {title || t("title")}
      </Title>
      <Input
        type="text"
        label={t("username-label")}
        onChange={(e) => (body.current.username = e.target.value)}
      />
      <Input
        type="password"
        label={t("password-label")}
        onChange={(e) => (body.current.password = e.target.value)}
      />
      <Button
        type="submit"
        className="w-full bg-blue-500 text-white p-3 rounded-lg hover:bg-blue-600 transition"
      >
        {t("submit-label")}
      </Button>
    </form>
  );
};

export default SignInForm;
