"use client";
import Input from "@/components/forms/input";
import Button from "@/components/forms/button";
import { signIn, signUp } from "@/services/authService";
import { useUserStore } from "@/store/userStore";
import { useRef } from "react";
import { Title } from "@/components/extra/Common";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";

const SignUpForm = () => {
  const t = useTranslations("Auth.Sign-up");
  const router = useRouter();
  const body = useRef<SingUpRequestType>({
    username: "",
    password: "",
    name: "",
  });
  const setUser = useUserStore((state) => state.setUser);

  const onSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    await signUp(body.current);
    const user = await signIn(body.current);
    setUser(user);
    router.push(FRONT_URL_PATH.INDEX);
  };

  return (
    <form
      onSubmit={onSubmit}
      className="md:min-w-[500px] max-w-md mx-auto bg-white py-3 px-6 rounded-lg space-y-4"
    >
      <Title className="text-2xl font-bold text-center text-gray-800 mb-4">
        {t("title")}
      </Title>
      <Input
        type="text"
        label={t("username-label")}
        onChange={(e) => (body.current.username = e.target.value)}
      />
      <Input
        type="password"
        label={t("password-label")}
        onChange={(e) => (body.current.password = e.target.value)}
      />
      <Input
        type="name"
        label={t("name-label")}
        onChange={(e) => (body.current.name = e.target.value)}
      />
      <Button
        type="submit"
        className="w-full bg-blue-500 text-white p-3 rounded-lg hover:bg-blue-600 transition"
      >
        {t("submit-label")}
      </Button>
    </form>
  );
};

export default SignUpForm;
