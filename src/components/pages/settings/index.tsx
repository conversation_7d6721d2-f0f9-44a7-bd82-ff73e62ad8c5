"use client";
import Button from "@/components/forms/button";
import Input from "@/components/forms/input";
import TextEditor from "@/components/forms/text-editor";
import FileUploader from "@/components/ui/file-uploader";
import Select from "@/components/ui/Select/select";
import { useRouter } from "@/i18n/routing";
import { DISTANCE_UNITS, WEIGHT_UNITS } from "@/lib/constants";
import { getFileUrl } from "@/lib/file";
import { updateProfile } from "@/services/userService";
import { useUserStore } from "@/store/userStore";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useMemo, useRef, useState } from "react";
import { FaUserCircle } from "react-icons/fa";

const Settings = () => {
  const t = useTranslations("Settings");
  const user = useUserStore((state) => state.user);
  const setUser = useUserStore((state) => state.setUser);

  const weightOptions: LabelValueType[] = useMemo(() => {
    return WEIGHT_UNITS.map((unit) => ({ label: unit, value: unit }));
  }, []);
  const distanceOptions: LabelValueType[] = useMemo(() => {
    return DISTANCE_UNITS.map((unit) => ({ label: unit, value: unit }));
  }, []);
  const params = useRef<Partial<UserUpdateType>>({});
  const [image, setImage] = useState<FileType | undefined>();

  const onFileUploaded = ({
    fileId,
    files,
  }: {
    fileId: string;
    files: FileType[];
  }) => {
    if (files != null && files.length > 0) {
      const [file] = files;
      setImage(file);
      params.current.imageId = fileId;
    }
  };

  const onSave = async () => {
    if (!params.current.name) {
      return alert(t("alert-name-missing"));
    }
    await updateProfile(params.current as UserUpdateType);
    setUser({
      ...user,
      ...params.current,
      image,
    } as UserType);
    alert(t("complete-save"));
  };

  useEffect(() => {
    if (user) {
      setImage(user.image);
      params.current = {
        name: user.name,
        weightUnit: user.weightUnit || weightOptions[0].value,
        distanceUnit: user.distanceUnit || distanceOptions[0].value,
        imageId: user?.image?.fileId,
      };
    }
  }, [user]);

  return (
    <section className="p-3">
      <div className="place-items-center">
        <FileUploader dragNdrop onFileDrop={onFileUploaded} accept="image/*">
          {image ? (
            <img
              style={{ borderRadius: "50%", aspectRatio: 1 }}
              src={getFileUrl(image)}
              width={192}
              height={192}
              alt="profile-image"
            />
          ) : (
            <FaUserCircle className="w-full h-[12rem] mb-5 text-2xl text-gray-600" />
          )}
        </FileUploader>
      </div>
      <div>
        <label className="ml-1 text-sm">{t("basic-information")}</label>
        <Input
          className="w-full"
          label={t("name-label")}
          defaultValue={user?.name}
          onChange={(e) => (params.current.name = e.target.value)}
        />
      </div>
      <div>
        <label className="ml-1 text-sm">{t("base-unit")}</label>
        <div className="flex gap-2">
          <Select
            className="w-full"
            options={weightOptions}
            defaultValue={user?.weightUnit}
            onChange={(value) =>
              (params.current.weightUnit = value as WeightUnit)
            }
          />
          <Select
            className="w-full"
            options={distanceOptions}
            defaultValue={user?.distanceUnit}
            onChange={(value) =>
              (params.current.distanceUnit = value as DistanceUnit)
            }
          />
        </div>
      </div>
      <div className="mt-3 w-full">
        <Button $minWidth="100%" onClick={onSave}>
          저장
        </Button>
      </div>
    </section>
  );
};

export default Settings;
