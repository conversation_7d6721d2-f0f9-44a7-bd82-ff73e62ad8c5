import Button from "@/components/forms/button";
import { useLocale, useTranslations } from "next-intl";
import { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import Modal from "@/components/ui/modal";

import Table from "@/components/ui/table";
import { getMyEquipmentList } from "@/services/equipmentService";
import { formatNumber } from "@/lib/number";
import { useUserStore } from "@/store/userStore";
import { EquipmentList } from "../List";

interface IEquipmentListModal {
  onSelect: (items: EquipmentListItemType[]) => void;
}
const EquipmentListModal = forwardRef<IModalRef, IEquipmentListModal>(
  ({ onSelect }, ref) => {
    const t = useTranslations("Measure");
    const user = useUserStore((state) => state.user);
    const locale = useLocale();
    const tableRef = useRef<TableRef>(null);
    const [equipmentList, setEquipmentList] = useState<EquipmentListItemType[]>(
      []
    );

    const retrieveEquipmentList = async () => {
      const { items } = await getMyEquipmentList({ locale });
      setEquipmentList(items);
    };

    useEffect(() => {
      if (user) {
        retrieveEquipmentList();
      }
    }, [user]);

    const onClickBring = () => {
      onSelect(tableRef.current?.getCheckedList() as EquipmentListItemType[]);
      if (ref && typeof ref !== "function") {
        ref.current?.closeModal();
      }
    };

    const tableProps = {
      checkable: true,
      items: equipmentList,
    };

    return (
      <Modal title={t("get-from-gear")} ref={ref}>
        <section className="bg-white min-w-[300px] flex flex-col gap-3">
          <EquipmentList ref={tableRef} {...tableProps} />

          <Button onClick={onClickBring}>
            <div className="flex items-center justify-center gap-2">
              <span>{t("bring")}</span>
            </div>
          </Button>
        </section>
      </Modal>
    );
  }
);

export default EquipmentListModal;
