import Button from "@/components/forms/button";
import { FloppyDisk } from "@/components/ui/icons";
import { useTranslations } from "next-intl";
import { forwardRef, useState } from "react";
import Modal from "@/components/ui/modal";

import MeasureExtra from "./Extra";
import { useUserStore } from "@/store/userStore";
import { createHistory } from "@/services/historyService";

interface IConfirmModal {
  items: EquipmentListItemType[];
  typeOptions: LabelValueType[];
}
const ConfirmModal = forwardRef<IModalRef, IConfirmModal>(
  ({ items, typeOptions }, ref) => {
    const user = useUserStore((state) => state.user);
    const [item, setItem] = useState<Partial<HistoryCreateType>>({
      unit: user?.distanceUnit || "km",
    });
    const t = useTranslations("Measure.Confirm");

    const onSave = async () => {
      const body: HistoryCreateRequestType = {
        ...item,
        details: items,
      };
      if (!body.name?.trim()) {
        return alert(t("alert-name-missing"));
      }
      if (!body.details || body.details.length === 0) {
        return alert(t("alert-details-missing"));
      }
      try {
        await createHistory(body);
        alert(t("alert-save"));
        if (ref && typeof ref !== "function") {
          ref.current?.closeModal();
        }
      } catch (e) {
        console.error(e);
        alert("TODO");
      }
    };

    return (
      <Modal ref={ref}>
        <section className="bg-white min-w-[300px] flex flex-col gap-3">
          <MeasureExtra
            item={item as HistoryCreateType}
            onChange={(next) => setItem((prev) => ({ ...prev, ...next }))}
          />

          <Button onClick={onSave}>
            <div className="flex items-center justify-center gap-2">
              <FloppyDisk />
              <span>{t("save")}</span>
            </div>
          </Button>
        </section>
      </Modal>
    );
  }
);

export default ConfirmModal;
