"use client";
import { CardWrapper, SubTitle } from "@/components/extra/Common";
import Button, { BubbleButton } from "@/components/forms/button";
import { BackPackIcon2, DrawerIcon } from "@/components/ui/icons";
import { useRouter } from "@/i18n/routing";
import {
  onChangeListState,
  onRemoveItemInListState,
} from "@/lib/change-list-state";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getEquipmentFavItemList } from "@/services/equipmentService";
import { useUserStore } from "@/store/userStore";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import EquipmentCreate from "../Create";
import EquipmentDonutChart from "../DonutChart";
import EquipmentEditableList from "../List";
import EquipmentSummary from "../Summary";
import ConfirmModal from "./ConfirmModal";
import EquipmentListModal from "./EquipmentListModal";
import EquipmentPresetModal from "./PresetModal";

const Measure = ({ typeOptions }: { typeOptions: LabelValueType[] }) => {
  const t = useTranslations("Measure");
  const user = useUserStore((state) => state.user);
  const router = useRouter();
  const [items, setItems] = useState<EquipmentListItemType[]>([]);
  const [favItems, setFavItems] = useState<EquipmentFavItemType[]>([]);
  const equipmentListModalRef = useRef<IModalRef>(null);
  const presetModalRef = useRef<IModalRef>(null);
  const confirmModalRef = useRef<IModalRef>(null);
  const locale = useLocale();

  useEffect(() => {
    const fetchFavItems = async () => {
      try {
        const { items } = await getEquipmentFavItemList({ locale });
        setFavItems(items);
      } catch (e) {
        console.error("Failed to fetch favorite equipment items", e);
      }
    };

    fetchFavItems();
  }, [locale]);

  const onSelect = (items: EquipmentListItemType[]) => {
    setItems((prev) => [...prev, ...items]);
  };
  const onClickInstantButton = (item: any) => {
    const idx = items.findIndex(
      (i: any) => i.equipmentFavItemId === item.equipmentFavItemId
    );
    if (idx === -1) {
      onChangeListState(setItems, item);
    } else {
      onChangeListState(setItems, items[idx].count + 1, idx, "count");
    }
  };

  return (
    <>
      <MeasureWrapper>
        <div id="index" className="flex justify-between gap-3">
          <Button
            className="flex flex-col justify-between items-center w-full gap-2"
            $theme="white"
            onClick={() => {
              if (user) {
                equipmentListModalRef.current?.openModal();
              } else {
                router.push(FRONT_URL_PATH.SIGNIN);
              }
            }}
          >
            <BackPackIcon2 />
            <span>{t("get-from-gear")}</span>
          </Button>
          <Button
            className="flex flex-col justify-between items-center w-full gap-2"
            $theme="white"
            onClick={() => {
              if (user) {
                presetModalRef.current?.openModal();
              } else {
                router.push(FRONT_URL_PATH.SIGNIN);
              }
            }}
          >
            <DrawerIcon />
            <span>{t("get-from-preset")}</span>
          </Button>
        </div>

        <CardWrapper
          id="add-data"
          style={{
            flexDirection: "row",
            flexWrap: "wrap",
            justifyContent: "space-between",
          }}
        >
          <EquipmentCreate
            saveButton={t("add-data")}
            typeOptions={typeOptions}
            onSave={(params) => onChangeListState(setItems, params)}
            buttonClass="w-[47%]"
          />
          {/* <div className="mt-3"> */}
          <Button
            $theme="gray"
            className="w-[47%]"
            onClick={() => {
              if (user) {
                confirmModalRef.current?.openModal();
              } else {
                router.push(FRONT_URL_PATH.SIGNIN);
              }
            }}
          >
            {t("save-label")}
          </Button>
          {/* </div> */}
          <hr className="my-3 w-full" />
          <div style={{ minWidth: 0 }}>
            <SubTitle>{t("items-taken-often")}</SubTitle>
            <ScrollArea>
              {favItems.map((b, idx) => (
                <BubbleButton
                  $theme="gray"
                  key={`INSTANT_BUTTONS_${idx}`}
                  onClick={() => onClickInstantButton(b)}
                >
                  {b.name}
                </BubbleButton>
              ))}
            </ScrollArea>
          </div>
        </CardWrapper>
        <CardWrapper id="summary">
          <EquipmentSummary items={items} />
        </CardWrapper>
        <CardWrapper id="chart">
          <EquipmentDonutChart
            items={
              items.map((item) => ({
                ...item,
                type: typeOptions.find(
                  (to) => to.value === item.equipmentTypeId
                )?.label,
              })) as EquipmentListItemType[]
            }
          />
        </CardWrapper>
        <CardWrapper id="table">
          {/* <Title>{t("my-backpack")}</Title> */}
          <EquipmentEditableList
            title={t("my-backpack")}
            typeOptions={typeOptions}
            items={items}
            onDelete={(idx) => onRemoveItemInListState(setItems, idx)}
            onChange={(params: Partial<EquipmentListItemType>, idx: number) =>
              Object.entries(params).forEach(([key, value]) => {
                onChangeListState(
                  setItems,
                  value,
                  idx,
                  key as keyof EquipmentListItemType
                );
              })
            }
          />
        </CardWrapper>
      </MeasureWrapper>
      <EquipmentListModal ref={equipmentListModalRef} onSelect={onSelect} />
      <EquipmentPresetModal ref={presetModalRef} onSelect={onSelect} />
      <ConfirmModal
        ref={confirmModalRef}
        items={items}
        typeOptions={typeOptions}
      />
    </>
  );
};

export default Measure;

const MeasureWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  @media (min-width: 767px) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    & > #index {
      grid-column: 1 / 2;
      grid-row: 1 / 2;
    }
    & > #add-data {
      grid-column: 1 / 2;
      grid-row: 2 / 3;
    }
    & > #summary {
      grid-column: 1 / 2;
      grid-row: 3 / 4;
    }
    & > #chart {
      grid-column: 1 / 2;
      grid-row: 4 / 5;
    }
    & > #table {
      max-width: unset;
      grid-column: 2 / 4;
      grid-row: 1 / 5;
    }
  }
`;

const ScrollArea = styled.div`
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;

  display: flex;
  flex-direction: row;
  gap: 8px;
  flex-wrap: nowrap;

  padding: 8px 0;

  & > * {
    flex-shrink: 0;
    white-space: nowrap;
  }

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
  }

  -webkit-overflow-scrolling: touch;
`;
