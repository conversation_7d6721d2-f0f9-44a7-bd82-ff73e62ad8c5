import DateTimeRange from "@/components/forms/datetime-range";
import Input from "@/components/forms/input";
import Select from "@/components/ui/Select/select";
import { DISTANCE_UNITS } from "@/lib/constants";
import { useTranslations } from "next-intl";

interface IMeasureExtra {
  item: HistoryCreateType;
  onChange?: (params: Partial<HistoryCreateType>) => void;
  readOnly?: boolean;
}
const MeasureExtra = ({ readOnly, item, onChange }: IMeasureExtra) => {
  const t = useTranslations("Measure.Extra");

  return (
    <div>
      <DateTimeRange
        readOnly={readOnly}
        type="datetime-local"
        defaultValue={{ start: item.startAt, end: item.endAt }}
        onChange={({ start, end }) =>
          onChange && onChange({ startAt: start, endAt: end })
        }
      />
      <Input
        label={t("name-label")}
        readOnly={readOnly}
        defaultValue={item.name}
        onChange={(e) => onChange && onChange({ name: e.target.value })}
      />
      <Input
        label={t("location-label")}
        readOnly={readOnly}
        defaultValue={item.location}
        onChange={(e) => onChange && onChange({ location: e.target.value })}
      />
      <div className="flex gap-2">
        <Input
          type="number"
          className="w-full"
          label={t("distance-label")}
          defaultValue={item.distance}
          onChange={(e) => onChange && onChange({ distance: +e.target.value })}
          readOnly={readOnly}
        />
        <Select
          readOnly={readOnly}
          defaultValue={item.unit}
          onChange={(value) =>
            onChange && onChange({ unit: value as DistanceUnit })
          }
          options={DISTANCE_UNITS.map((unit) => ({
            label: unit,
            value: unit,
          }))}
        />
      </div>
    </div>
  );
};

export default MeasureExtra;
