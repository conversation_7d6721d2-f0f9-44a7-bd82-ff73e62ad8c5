import Button from "@/components/forms/button";
import { useLocale, useTranslations } from "next-intl";
import { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import Modal from "@/components/ui/modal";
import { getUserEquipmentPresetList } from "@/services/equipmentService";
import Table from "@/components/ui/table";
import { formatNumber } from "@/lib/number";
import RadioButton from "@/components/forms/radio-button";
import { useUserStore } from "@/store/userStore";
import { EquipmentList } from "../List";

interface IEquipmentPresetModal {
  onSelect: (items: EquipmentListItemType[]) => void;
}
const EquipmentPresetModal = forwardRef<IModalRef, IEquipmentPresetModal>(
  ({ onSelect }, ref) => {
    const t = useTranslations("Measure");
    const user = useUserStore((state) => state.user);
    const locale = useLocale();
    const [presetList, setPresetList] = useState<UserEquipmentPresetItemType[]>(
      []
    );
    const [activeIndex, setActiveIndex] = useState<number>();

    const retrievePresetList = async () => {
      const { items } = await getUserEquipmentPresetList({ locale });
      setPresetList(items);
    };

    useEffect(() => {
      if (user) {
        retrievePresetList();
      }
    }, [user]);

    const onChangeRadioButton = (idx: number) => setActiveIndex(idx);

    const radioOptions: LabelValueType[] = useMemo(
      () =>
        presetList.map((pl, idx) => ({
          label: pl.title,
          value: idx,
        })),
      [presetList]
    );

    const onClickBring = () => {
      if (activeIndex === undefined) {
        return alert(t("alert-didnt-check-preset"));
      }
      onSelect([...presetList[activeIndex].items]);
      if (ref && typeof ref !== "function") {
        ref.current?.closeModal();
      }
    };

    const tableProps = useMemo(
      () => ({
        className: "h-[200px]",
        items: activeIndex !== undefined ? presetList[activeIndex]?.items : [],
      }),
      [presetList, activeIndex]
    );

    return (
      <Modal title={t("get-from-preset")} ref={ref}>
        <section className="bg-white min-w-full md:min-w-[300px] w-full md:w-[500px] flex flex-col gap-3">
          <RadioButton options={radioOptions} onChange={onChangeRadioButton} />

          <EquipmentList {...tableProps} />

          <Button onClick={onClickBring}>
            <div className="flex items-center justify-center gap-2">
              <span>{t("bring")}</span>
            </div>
          </Button>
        </section>
      </Modal>
    );
  }
);

export default EquipmentPresetModal;
