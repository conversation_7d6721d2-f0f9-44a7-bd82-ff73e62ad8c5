"use client";
import { Title } from "@/components/extra/Common";
import NoData from "@/components/ui/no-data";
import Pager from "@/components/ui/pager";
import Table from "@/components/ui/table";
import { useAuthGuard } from "@/hooks/useAuthGuard";
import { Link, useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getStartEndString } from "@/lib/date";
import { getMyHistories } from "@/services/historyService";
import { useTranslations } from "next-intl";
import { useEffect, useMemo, useState } from "react";

const EquipmentHistoryList = () => {
  const t = useTranslations("History");
  const isAuthenticated = useAuthGuard();
  const router = useRouter();

  const [query, setQuery] = useState<{ page: number; size: number }>({
    page: 0,
    size: 10,
  });
  const [items, setItems] = useState<HistoryListItemType[]>([]);
  const [totalCount, setTotalCount] = useState(0);

  const retrieveHistories = async () => {
    const { items, totalCount } = await getMyHistories({ ...query });
    setItems(items);
    setTotalCount(totalCount);
  };

  const columns: TableColumnType[] = useMemo(
    () => [
      { label: t("name-label"), value: "name", style: { textAlign: "center" } },
      { label: t("time-label"), value: "time", style: { textAlign: "center" } },
      {
        label: t("location-label"),
        value: "location",
        style: { textAlign: "center" },
      },
      {
        label: t("distance-label"),
        value: "distances",
        style: { textAlign: "center" },
      },
    ],
    []
  );

  const tableProps: ITable = {
    columns,
    items: items.map((item) => ({
      ...item,
      time: getStartEndString(item.startAt, item.endAt),
      distances: `${item.distance ? `${item.distance}` : "-"} ${item.unit}`,
      location: item.location || "-",
    })),
    onClick(item) {
      router.push(`${FRONT_URL_PATH.GEAR_HISTORY}/${item.historyId}`);
    },
  };
  const pagerProps: IPager = {
    ...query,
    totalCount,
    onPageChange(newPage) {
      setQuery((prev) => ({ ...prev, page: newPage }));
    },
  };

  useEffect(() => {
    if (isAuthenticated) {
      retrieveHistories();
    }
  }, [isAuthenticated, query]);
  if (!isAuthenticated) {
    return <NoData />;
  }
  return (
    <>
      <Title>{t("title")}</Title>
      <div className="hidden md:block">
        <Table {...tableProps} />
      </div>
      <div className="block md:hidden">
        <ul>
          {tableProps.items.map((item, itemKey) => (
            <li key={`HISTORIES_MOBILE_LIST_${itemKey}`}>
              <Link href={`${FRONT_URL_PATH.GEAR_HISTORY}/${item.historyId}`}>
                <div className="p-2 my-2 rounded-lg shadow-md bg-white duration-200 hover:bg-gray-100">
                  <h3 className="text-lg font-bold">{item.name}</h3>
                  <div className="text-sm">{item.location}</div>
                  <div className="text-sm flex gap-3">
                    <span>{item.time}</span>
                    <span>{item.distances}</span>
                  </div>
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
      <Pager {...pagerProps} />
    </>
  );
};

export default EquipmentHistoryList;
