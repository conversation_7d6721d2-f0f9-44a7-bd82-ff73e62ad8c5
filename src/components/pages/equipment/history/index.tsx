"use client";
import { Title } from "@/components/extra/Common";
import { useTranslations } from "next-intl";
import EquipmentDonutChart from "../DonutChart";
import { EquipmentList } from "../List";
import { getStartEndString } from "@/lib/date";

interface IEquipmentHistory {
  item: HistoryItemType;
}
const EquipmentHistory = ({ item }: IEquipmentHistory) => {
  const t = useTranslations("History");
  return (
    <>
      <div className="bg-white p-4">
        <Title>{item.name}</Title>
        <hr className="my-3" />
        <div className="flex items-center gap-3">
          <span>{item.location || "-"}</span>
          <span>{getStartEndString(item.startAt, item.endAt, "P p ccc")}</span>
          <span>
            {item.distance ? `${item.distance}` : "-"} {item.unit}
          </span>
        </div>
        <hr className="my-3" />
        <div>
          <EquipmentDonutChart
            items={item.details as unknown as EquipmentListItemType[]}
          />
        </div>
      </div>
      <div>
        <EquipmentList
          items={item.details as unknown as EquipmentListItemType[]}
        />
      </div>
    </>
  );
};

export default EquipmentHistory;
