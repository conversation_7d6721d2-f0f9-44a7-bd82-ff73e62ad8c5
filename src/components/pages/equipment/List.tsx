"use client";
import { useTranslations } from "next-intl";
import { CardWrapper, Title } from "@/components/extra/Common";
import Image from "next/image";
import WhiteInput from "@/components/forms/white-input";
import { WEIGHT_UNITS } from "@/lib/constants";
import Table from "@/components/ui/table";
import { forwardRef, useMemo } from "react";
import WhiteSelect from "@/components/ui/Select/white-select";
import Button from "@/components/forms/button";
import { FloppyDisk, Trash } from "@/components/ui/icons";
import { formatNumber } from "@/lib/number";

interface IEquipmentEditableList {
  title?: string;
  items: EquipmentListItemType[];
  typeOptions: LabelValueType[];
  onChange?: (params: Partial<EquipmentListItemType>, idx: number) => void;
  onSave?: (params: any) => void;
  onDelete?: (idx: number) => void;
}

const EquipmentEditableList = ({
  title,
  items,
  typeOptions,
  onChange,
  onSave,
  onDelete,
}: IEquipmentEditableList) => {
  const t = useTranslations("Equipment.List");
  const tc = useTranslations("Equipment.Create");
  const columns: TableColumnType[] = useMemo(() => {
    const _columns = [
      {
        label: tc("brand-label"),
        value: "brandName",
        style: { padding: 0, minWidth: "120px" },
      },
      {
        label: tc("title-label"),
        value: "name",
        style: { padding: 0, minWidth: "150px" },
      },
      {
        label: tc("weight-label"),
        value: "weight",
        style: { padding: 0, minWidth: "90px" },
      },
      { label: tc("unit-label"), value: "unit", style: { padding: 0 } },
      {
        label: tc("type-label"),
        value: "type",
        style: { padding: 0, minWidth: "90px" },
      },
      {
        label: tc("quantity-label"),
        value: "count",
        style: { padding: 0, textAlign: "center", textAlignLast: "center" },
      },
    ];
    if (onDelete) {
      _columns.push({
        label: tc("delete-label"),
        value: "delete",
        style: { padding: 0, textAlign: "center" } as any,
      });
    }
    return _columns;
  }, [onDelete]);
  const tableProps: ITable = useMemo(
    () => ({
      columns,
      items: items.map((item, idx) => ({
        brandName: (
          <WhiteInput
            defaultValue={item.brandName}
            onChange={(e) =>
              onChange && onChange({ brandName: e.target.value }, idx)
            }
          />
        ),
        name: (
          <WhiteInput
            defaultValue={item.name}
            onChange={(e) =>
              onChange && onChange({ name: e.target.value }, idx)
            }
          />
        ),
        type: (
          <WhiteSelect
            options={typeOptions}
            defaultValue={item.equipmentTypeId}
            onChange={(value) =>
              onChange && onChange({ equipmentTypeId: value }, idx)
            }
          />
        ),
        weight: (
          <WhiteInput
            type="number"
            min={0}
            defaultValue={item.weight}
            onChange={(e) =>
              onChange && onChange({ weight: +e.target.value }, idx)
            }
          />
        ),
        unit: (
          <WhiteSelect
            options={WEIGHT_UNITS.map((unit) => ({ label: unit, value: unit }))}
            defaultValue={item.unit}
            onChange={(value) =>
              onChange && onChange({ unit: value as WeightUnit }, idx)
            }
          />
        ),
        count: (
          <WhiteInput
            type="number"
            min={1}
            step={1}
            defaultValue={item.count}
            onChange={(e) =>
              onChange && onChange({ count: +e.target.value }, idx)
            }
          />
        ),
        delete: (
          <Button
            onClick={(e) => [
              e.stopPropagation(),
              e.preventDefault(),
              onDelete && onDelete(idx),
            ]}
            $theme="white"
            $fontSize="22px"
            $padding="6px"
            $margin="6px"
          >
            <Trash />
          </Button>
        ),
      })),
    }),
    [items]
  );
  return (
    <>
      <div className="flex justify-between items-center mb-3">
        <Title>{title || t("title")}</Title>
        {onSave && (
          <Button
            $fontSize="12px"
            $padding="10px"
            $margin="4px"
            onClick={onSave}
          >
            <FloppyDisk />
          </Button>
        )}
      </div>

      <Table {...tableProps} />
    </>
  );
};

export default EquipmentEditableList;

interface IEquipmentList {
  items: EquipmentListItemType[];
  checkable?: boolean;
  className?: string;
}
export const EquipmentList = forwardRef<TableRef, IEquipmentList>(
  ({ items, checkable = false, className }, ref) => {
    const tc = useTranslations("Equipment.Create");

    const columns: TableColumnType[] = useMemo(
      () => [
        {
          label: tc("brand-label"),
          value: "brandName",
          style: { minWidth: "120px" },
        },
        {
          label: tc("title-label"),
          value: "name",
          style: { minWidth: "150px" },
        },
        {
          label: tc("weight-label"),
          value: "weight_unit",
          style: { minWidth: "90px", textAlign: "center" },
        },
        {
          label: tc("type-label"),
          value: "type",
          style: { minWidth: "90px", textAlign: "center" },
        },
        {
          label: tc("quantity-label"),
          value: "count",
          style: { textAlign: "center" },
        },
      ],
      []
    );

    const tableProps: ITable = useMemo(
      () => ({
        checkable,
        columns,
        items: items.map((item) => ({
          ...item,
          weight_unit: `${formatNumber(item.weight)}${item.unit}`,
        })),
        className,
      }),
      [items]
    );
    return <Table ref={ref} {...tableProps} />;
  }
);

export const EquipmentListItem = ({
  name,
  weight,
  unit,
  type,
  count,
}: EquipmentListItemType) => {
  return (
    <div className="flex items-center space-x-3 py-2 border-0 border-b">
      <div className="m-auto">
        <Image width={50} height={50} src={`/logo.png`} alt="이미지" />
      </div>
      <div className="w-[80%]">
        <div className="flex justify-between">
          <b>{name}</b>
          <b>
            {weight}
            {unit}
          </b>
        </div>
        <div className="flex justify-between">
          <span>{type}</span>
          <span>{count}</span>
        </div>
      </div>
    </div>
  );
};
