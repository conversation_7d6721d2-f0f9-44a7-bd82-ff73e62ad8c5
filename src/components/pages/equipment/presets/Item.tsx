import { Title } from "@/components/extra/Common";
import { getUserEquipmentPresetItem } from "@/services/equipmentService";
import { useEffect, useId, useState } from "react";
import { EquipmentListItem } from "../List";
import { useLocale } from "next-intl";

interface IEquipmentPresetItem {
  userEquipmentSetId: number;
}
/**
 *
 * @deprecated
 */
const EquipmentPresetItem = ({ userEquipmentSetId }: IEquipmentPresetItem) => {
  const id = useId();
  const locale = useLocale();
  const [item, setItem] = useState<UserEquipmentPresetItemType>();
  const getData = async () => {
    const item = await getUserEquipmentPresetItem({
      userEquipmentSetId,
      locale,
    });
    setItem(item);
  };
  useEffect(() => {
    getData();
  }, [userEquipmentSetId]);
  if (!item) {
    return <></>;
  }
  return (
    <div className="min-h-[200px]">
      <Title>{item.title}</Title>
      <ul>
        {item.items.map((item, itemKey) => (
          <li key={`PRESET_${id}_EQUIPMENT_LIST_${itemKey}`}>
            <EquipmentListItem {...item} />
          </li>
        ))}
        <></>
      </ul>
    </div>
  );
};
export default EquipmentPresetItem;
