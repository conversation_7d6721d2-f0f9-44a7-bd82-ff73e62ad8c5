import { ImageWrapper, Title } from "@/components/extra/Common";
import Button from "@/components/forms/button";
import { useLocale } from "next-intl";
import {
  useCallback,
  useEffect,
  useId,
  useMemo,
  useRef,
  useState,
} from "react";
import { PiFloppyDiskBold, PiPlus } from "react-icons/pi";
import { EquipmentListItem } from "../List";
import Dropdown from "@/components/ui/dropdown";
import useComponentSize from "@/hooks/useComponentSize";
import Image from "next/image";
import { BiDownArrow, BiUpArrow } from "react-icons/bi";
import {
  onChangeListState,
  onRemoveItemInListState,
} from "@/lib/change-list-state";
import WhiteInput from "@/components/forms/white-input";
import { FiTrash } from "react-icons/fi";
import NoData from "@/components/ui/no-data";
import {
  DownArrow,
  FloppyDisk,
  Plus,
  Trash,
  UpArrow,
} from "@/components/ui/icons";

interface IEquipmentPresetCreate {
  userEquipmentSetId?: number;
  title?: string;
  items?: EquipmentListItemType[];
  equipmentList: EquipmentListItemType[];
  onChange?: (value: Partial<UserEquipmentPresetItemType>) => void;
  onSave?: () => void;
  onDelete?: () => void;
}
const EquipmentPresetCreate = ({
  userEquipmentSetId,
  title = "",
  items = [],
  equipmentList: _equipmentList,
  onChange,
  onSave,
  onDelete,
}: IEquipmentPresetCreate) => {
  const id = useId();
  const ref = useRef<HTMLDivElement>(null);
  const { width } = useComponentSize(ref);

  const [equipmentList, setEquipmentList] = useState<EquipmentListItemType[]>([
    ..._equipmentList,
  ]);
  useEffect(() => {
    const next = [..._equipmentList]
      .map((el) => {
        const next = items.find(
          (item) => item.userEquipmentId === el.userEquipmentId
        );
        if (!next) {
          return el;
        }
        return {
          ...el,
          count: el.count - next.count,
        };
      })
      .filter((el) => el.count > 0);
    setEquipmentList(next);
  }, [items]);

  const onDecrement = (idx: number) => {
    if (!onChange) {
      return;
    }
    // 프리셋 리스트에서 갯수 1개 삭제
    const next = [...items];
    next[idx].count--;
    onChange({ userEquipmentSetId, title, items: next });

    // 장비목록에 있는 지 인덱스 확인
    const eIdx = equipmentList.findIndex(
      (el) => el.userEquipmentId === next[idx].userEquipmentId
    );
    if (eIdx !== -1) {
      // 장비목록에 있음 갯수 1개 추가
      onChangeListState(
        setEquipmentList,
        equipmentList[eIdx].count + 1,
        eIdx,
        "count"
      );
    } else {
      // 장비목록에 없으면 갯수 1개로 장비목록에 추가
      onChangeListState(setEquipmentList, { ...next[idx], count: 1 });
    }
  };
  const onIncrement = (idx: number) => {
    if (!onChange) {
      return;
    }
    // 프리셋 리스트에서 갯수 1개 추가
    const next = [...items];
    next[idx].count++;
    onChange({ userEquipmentSetId, title, items: next });

    // 장비목록에 있는 지 인덱스 확인
    const eIdx = equipmentList.findIndex(
      (el) => el.userEquipmentId === next[idx].userEquipmentId
    );
    // 장비목록에 있을 때
    if (eIdx !== -1) {
      // 장비 목록에 남은 갯수가 1개가 아니면 갯수 1개 삭제
      if (equipmentList[eIdx].count !== 1) {
        onChangeListState(
          setEquipmentList,
          equipmentList[eIdx].count - 1,
          eIdx,
          "count"
        );
      } else {
        // 장비 목록에 남은 갯수가 1개면 장비목록에서 삭제
        onRemoveItemInListState(setEquipmentList, eIdx);
      }
    }
  };
  const onDeleteItem = (idx: number) => {
    if (!onChange) {
      return;
    }
    // 프리셋 리스트에서 삭제
    const next = [...items];
    const [item] = next.splice(idx, 1);
    onChange({ userEquipmentSetId, title, items: next });

    // 장비목록에 있는 지 인덱스 확인
    const eIdx = equipmentList.findIndex(
      (el) => el.userEquipmentId === item.userEquipmentId
    );
    if (eIdx !== -1) {
      // 장비목록에 있으면 (삭제한 갯수 + 장비목록에 있는 갯수)로 숫자 맞추기
      // _equipmentList 여기서 가져와도 되긴 함
      onChangeListState(
        setEquipmentList,
        equipmentList[eIdx].count + item.count,
        eIdx,
        "count"
      );
    } else {
      // 장비목록에 없으면 장비목록에 추가
      onChangeListState(setEquipmentList, item);
    }
  };

  const dropdownOptions: DropDownOptionType[] = useMemo(
    () =>
      equipmentList.map((el) => ({
        value: el,
        label: <EquipmentListItem {...el} />,
        onClick: ({ value }: { value: EquipmentListItemType }) => {
          if (!onChange) {
            return;
          }
          const next = [...items];
          // 프리셋 리스트에 있는 지 확인
          const idx = items.findIndex(
            (item) => item.userEquipmentId === value.userEquipmentId
          );
          if (idx !== -1) {
            // 프리셋 리스트에 있으면 갯수 1개 추가
            next[idx].count++;
            onChange({ userEquipmentSetId, title, items: next });
          } else {
            // 프리셋 리스트에 없으면 갯수 1개로 프리셋 목록에 추가
            next.push({ ...value });
            next[next.length - 1].count = 1;
          }
          onChange({ userEquipmentSetId, title, items: next });

          // 장비목록의 인덱스 확인
          const eIdx = equipmentList.findIndex(
            (el) => el.userEquipmentId === value.userEquipmentId
          );
          // 장비목록에서 남은 갯수가
          if (equipmentList[eIdx].count === 1) {
            // 1개면 장비목록에서 삭제
            onRemoveItemInListState(setEquipmentList, eIdx);
          } else {
            // 1개가 아니면 갯수만 1개 줄이기
            onChangeListState(
              setEquipmentList,
              equipmentList[eIdx].count - 1,
              eIdx,
              "count"
            );
          }
        },
      })),
    [equipmentList]
  );

  return (
    <div ref={ref} className="min-h-[200px]">
      <div className="flex justify-between items-center">
        <Title className="w-full">
          {onChange ? (
            <WhiteInput
              $fontSize="18px"
              defaultValue={title}
              onChange={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onChange({ userEquipmentSetId, title: e.target.value, items });
              }}
            />
          ) : (
            title
          )}
        </Title>
        {onDelete && (
          <Button
            $fontSize="12px"
            $padding="10px"
            $margin="4px"
            $theme="red"
            onClick={onDelete}
          >
            <Trash />
          </Button>
        )}
        {onSave && (
          <Button
            $fontSize="12px"
            $padding="10px"
            $margin="4px"
            onClick={onSave}
          >
            <FloppyDisk />
          </Button>
        )}
        {onChange && (
          <Dropdown
            $dropdownAlign="right"
            $dropdownWidth={`${width}px`}
            $minWidth="20px"
            $theme="gray"
            $margin="4px"
            $padding="10px"
            fontSize="12px"
            options={dropdownOptions}
          >
            <Plus />
          </Dropdown>
        )}
      </div>
      <ul>
        {items?.map((item, itemKey) => {
          const itemOnList = _equipmentList.find(
            (el) => el.userEquipmentId === item.userEquipmentId
          );
          return (
            <li key={`PRESET_CREATE_${id}_EQUIPMENT_LIST_${itemKey}`}>
              {/* <EquipmentListItem {...item} /> */}
              <div className=" flex items-center space-x-3 py-2 border-0 border-b">
                <div className="m-auto">
                  <Image
                    width={50}
                    height={50}
                    src={`/logo.png`}
                    alt="이미지"
                  />
                </div>
                <div className="w-[80%]">
                  <div className="flex justify-between">
                    <b>{item.name}</b>
                    <b>
                      {item.weight}
                      {item.unit}
                    </b>
                  </div>
                  <div className="flex justify-between">
                    <span>{item.type}</span>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => onDecrement(itemKey)}
                        disabled={item.count <= 1}
                        className="p-1 rounded-md transition duration-200 hover:bg-gray-200 disabled:cursor-not-allowed disabled:bg-gray-100"
                      >
                        <DownArrow />
                      </button>

                      <span className="">{item.count}</span>

                      <button
                        onClick={() => onIncrement(itemKey)}
                        disabled={(itemOnList?.count || 1) <= item.count}
                        className="p-1 rounded-md transition duration-200 hover:bg-gray-200 disabled:cursor-not-allowed disabled:bg-gray-100"
                      >
                        <UpArrow />
                      </button>
                    </div>
                  </div>
                </div>
                <Button
                  $fontSize="12px"
                  $padding="10px"
                  $margin="4px"
                  $theme="red"
                  onClick={() => onDeleteItem(itemKey)}
                >
                  <FiTrash />
                </Button>
              </div>
            </li>
          );
        })}
      </ul>
      {items.length === 0 && <NoData />}
    </div>
  );
};

export default EquipmentPresetCreate;
