"use client";
import Tabs from "@/components/ui/tabs";
import {
  changeUserEquipmentPresetOrders,
  createUserEquipmentPresetItem,
  deleteUserEquipmentPresetItem,
  getUserEquipmentPresetList,
  updateUserEquipmentPresetItem,
} from "@/services/equipmentService";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import EquipmentPresetCreate from "./Create";
import {
  onChangeListState,
  onRemoveItemInListState,
} from "@/lib/change-list-state";
import EquipmentDonutChart from "../DonutChart";
import EquipmentSummary from "../Summary";
import { CardWrapper } from "@/components/extra/Common";
import styled from "styled-components";
import { useAuthGuard } from "@/hooks/useAuthGuard";
import NoData from "@/components/ui/no-data";
interface IEquipmentPreset {
  equipmentList: EquipmentListItemType[];
}
const EquipmentPresetList = ({ equipmentList }: IEquipmentPreset) => {
  const t = useTranslations("Equipment.Preset");
  const isAuthenticated = useAuthGuard();
  const locale = useLocale();
  const [items, setItems] = useState<UserEquipmentPresetItemType[]>([]);
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const tabsRef = useRef<TabsRef>(null);

  const getInitialPreset = useCallback(
    (num: number): UserEquipmentPresetItemType => {
      const title = `${t("new-preset")} ${num}`;
      const item = {
        title,
        items: [],
      };
      return item;
    },
    []
  );

  const getData = async () => {
    const { items } = await getUserEquipmentPresetList({ locale });
    setItems(items.length === 0 ? [getInitialPreset(1)] : items);
  };
  useEffect(() => {
    if (isAuthenticated) {
      getData();
    }
  }, [isAuthenticated]);

  const onClickAdd = () => {
    onChangeListState(setItems, getInitialPreset(items.length + 1));
  };
  const onChangeOrder = async (
    idx: number,
    tabList?: TabType<UserEquipmentPresetItemType>[]
  ) => {
    setActiveIndex(idx);
    if (tabList) {
      const next = tabList.map(({ value }) => value);
      setItems(next);
      const params: UserEquipmentPresetChangeOrderItemType[] = next
        .filter((item) => item.userEquipmentSetId !== undefined)
        .map((item, idx) => ({
          userEquipmentSetId: item.userEquipmentSetId!,
          order: idx + 1,
        }));
      await changeUserEquipmentPresetOrders({ items: params });
    }
  };

  const onSave = async (idx: number) => {
    const item = { ...items[idx] };
    if (!item.title?.trim()) {
      return alert("TODO");
    }
    if ((item.items?.length || 0) <= 0) {
      return alert("TODO");
    }
    const body: UserEquipmentPresetUpdate | UserEquipmentPresetCreate = {
      ...item,
      title: item.title.trim(),
      order: (tabsRef.current?.getActiveTab() || 0) + 1,
    };
    if (!item.userEquipmentSetId) {
      const { userEquipmentSetId } = await createUserEquipmentPresetItem(body);
      onChangeListState(
        setItems,
        userEquipmentSetId,
        idx,
        "userEquipmentSetId"
      );
    } else {
      await updateUserEquipmentPresetItem(body as UserEquipmentPresetUpdate);
    }
    alert(t("create-new-preset-message"));
  };

  const onDelete = async (idx: number) => {
    const item = { ...items[idx] };
    if (item.userEquipmentSetId) {
      await deleteUserEquipmentPresetItem(item.userEquipmentSetId);
    }
    onRemoveItemInListState(setItems, idx);
    if (tabsRef && tabsRef.current) {
      tabsRef.current?.setActiveTab(0);
    }
  };

  const tabs = useMemo(
    () => [
      ...items.map((item, idx) => ({
        value: item,
        // value: item,
        label: <span>&nbsp;{item.title}</span>,
        content: (
          <EquipmentPresetCreate
            {...items[idx]}
            equipmentList={[...equipmentList]}
            onChange={(value) => {
              onChangeListState(setItems, value, idx);
            }}
            onSave={() => onSave(idx)}
            onDelete={() => {
              if (window.confirm(t("delete-confirm-message"))) {
                onDelete(idx);
              }
            }}
          />
        ),
      })),
    ],
    [items]
  );
  if (!isAuthenticated) {
    return <NoData />;
  }
  return (
    <PresetWrapper>
      <CardWrapper id="summary">
        <EquipmentSummary items={items[activeIndex]?.items || []} />
      </CardWrapper>
      <CardWrapper id="chart">
        <EquipmentDonutChart items={items[activeIndex]?.items || []} />
      </CardWrapper>
      <div id="tabs">
        <Tabs
          ref={tabsRef}
          onClickAdd={onClickAdd}
          tabs={tabs}
          onChange={onChangeOrder}
        />
      </div>
    </PresetWrapper>
  );
};
export default EquipmentPresetList;

const PresetWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  @media (min-width: 767px) {
    display: grid;
    grid-template-columns: repeat(2, calc(50% - 5px));
    & > #summary {
      grid-column: 2 / 3;
      grid-row: 1 / 2;
    }
    & > #chart {
      grid-column: 2 / 3;
      grid-row: 2 / 3;
    }
    & > #tabs {
      grid-column: 1 / 2;
      grid-row: 1 / 3;
    }
  }
`;
