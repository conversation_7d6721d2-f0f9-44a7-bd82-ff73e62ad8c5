"use client";
import { <PERSON><PERSON>rap<PERSON>, Row, Title } from "@/components/extra/Common";
import Button from "@/components/forms/button";
import Input from "@/components/forms/input";
import Select from "@/components/ui/Select/select";
import { WEIGHT_UNITS } from "@/lib/constants";
import { saveMyEquipment } from "@/services/equipmentService";
import { useTranslations } from "next-intl";
import { useMemo, useRef } from "react";

const initialParams: EquipmentSaveRequestType = {
  brandName: "",
  name: "",
  weight: 0,
  unit: "g",
  count: 1,
};
const EquipmentCreate = ({
  typeOptions,
  saveButton,
  onSave,
  buttonClass
}: {
  typeOptions: LabelValueType[];
  saveButton?: string;
  onSave?: (params: EquipmentSaveRequestType) => void;
  buttonClass?: string;
}) => {
  const t = useTranslations("Equipment.Create");
  const params = useRef<EquipmentSaveRequestType>({
    ...initialParams,
  });
  const brandNameRef = useRef<InputRef>(null);
  const nameRef = useRef<InputRef>(null);
  const weightRef = useRef<InputRef>(null);
  const quantityRef = useRef<InputRef>(null);
  const typeRef = useRef<SelectRef>(null);
  const unitRef = useRef<SelectRef>(null);

  const selectProps = useMemo(
    () => ({
      options: typeOptions,
      onChange: (value: string) =>
        (params.current.equipmentTypeId = value as string),
    }),
    [typeOptions]
  );
  const unitProps = useMemo(
    () => ({
      defaultValue: initialParams.unit,
      options: WEIGHT_UNITS.map((unit) => ({ label: unit, value: unit })),
      onChange: (value: string) => (params.current.unit = value as WeightUnit),
    }),
    []
  );
  const saveEquipment = async (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    // TODO: 밸리데이션
    if (onSave) {
      onSave({ ...params.current });
      params.current = { ...initialParams };
      [brandNameRef, nameRef, weightRef, quantityRef, typeRef, unitRef].forEach(
        (ref) => {
          if (ref && ref.current) {
            ref.current.clear();
          }
        }
      );
    }
  };
  return (
    <>
      {/*  */}
      <div className="w-full">
        <Title>{t("title")}</Title>
        <Input
          ref={brandNameRef}
          label={t("brand-label")}
          onChange={(e) => (params.current.brandName = e.target.value)}
        />
        <Input
          ref={nameRef}
          label={t("title-label")}
          onChange={(e) => (params.current.name = e.target.value)}
        />
        <Select ref={typeRef} className="w-full" {...selectProps} />
        <Row $gap="10px">
          <Row className="w-[75%]" $gap="10px">
            <Input
              ref={weightRef}
              label={t("weight-label")}
              type="number"
              onChange={(e) => (params.current.weight = +e.target.value)}
            />
            <Select ref={unitRef} {...unitProps} />
          </Row>
          <Input
            ref={quantityRef}
            className="w-[25%]"
            type="number"
            step={1}
            min={1}
            label={t("quantity-label")}
            onChange={(e) => (params.current.count = +e.target.value)}
          />
        </Row>
      </div>
      <Button className={buttonClass} onClick={saveEquipment}>{saveButton || t("save")}</Button>
    </>
  );
};

export default EquipmentCreate;
