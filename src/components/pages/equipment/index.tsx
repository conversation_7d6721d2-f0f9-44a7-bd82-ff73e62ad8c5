"use client";
import {
  deleteMyEquipment,
  getMyEquipmentList,
  saveMyEquipment,
  updateMyEquipment,
} from "@/services/equipmentService";
import EquipmentCreate from "./Create";
import EquipmentSummary from "./Summary";
import EquipmentDonutChart from "./DonutChart";
import EquipmentEditableList from "./List";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { CardWrapper } from "@/components/extra/Common";
import styled from "styled-components";
import { useAuthGuard } from "@/hooks/useAuthGuard";
import NoData from "@/components/ui/no-data";
import { onChangeListState } from "@/lib/change-list-state";

const Equipment = ({ typeOptions }: { typeOptions: LabelValueType[] }) => {
  const t = useTranslations("Equipment");
  const isAuthenticated = useAuthGuard();
  const [items, setItems] = useState<EquipmentListItemType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const locale = useLocale();
  const getListData = async () => {
    setLoading(true);
    const { items } = await getMyEquipmentList({ locale });
    setItems(items);
    setLoading(false);
  };

  const onSave = async (params: EquipmentSaveRequestType) => {
    try {
      await saveMyEquipment(params);
      getListData();
    } catch (e) {
      alert(t("fail-complete"));
    }
  };
  const onSaveList = async () => {
    try {
      await Promise.all(items.map((item) => updateMyEquipment(item)));

      getListData();
      alert(t("save-complete"));
    } catch (e) {
      alert(t("fail-complete"));
    }
  };
  const onDelete = async (idx: number) => {
    const userEquipmentId = items[idx].userEquipmentId;
    const name = items[idx].name;
    if (confirm(t("ask-delete", { name }))) {
      await deleteMyEquipment(userEquipmentId);
      getListData();
    }
  };
  const onChange = async (
    params: Partial<EquipmentListItemType>,
    idx: number
  ) => {
    Object.entries(params).forEach(([key, value]) => {
      onChangeListState(
        setItems,
        value,
        idx,
        key as keyof EquipmentListItemType
      );
    });
  };

  useEffect(() => {
    if (isAuthenticated) {
      getListData();
    }
  }, [isAuthenticated]);

  if (!isAuthenticated) {
    return <NoData />;
  }

  return (
    <EquipmentWrapper>
      <CardWrapper id="create">
        <EquipmentCreate typeOptions={typeOptions} onSave={onSave} />
      </CardWrapper>
      <CardWrapper id="summary">
        <EquipmentSummary items={items} />
      </CardWrapper>
      <CardWrapper id="chart">
        <EquipmentDonutChart items={items} />
      </CardWrapper>
      <div id="table">
        <EquipmentEditableList
          typeOptions={typeOptions}
          items={items}
          onSave={onSaveList}
          onChange={onChange}
          onDelete={onDelete}
        />
      </div>
    </EquipmentWrapper>
  );
};

export default Equipment;

const EquipmentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  & > #table {
    // background-color: white;
    width: 100%;
    height: 100%;
  }
  @media (min-width: 767px) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    & > #create {
      grid-column: 1 / 2;
      grid-row: 1 / 2;
    }
    & > #summary {
      grid-column: 1 / 2;
      grid-row: 2 / 3;
    }
    & > #chart {
      grid-column: 1 / 2;
      grid-row: 3 / 4;
    }
    & > #table {
      grid-column: 2 / 4;
      grid-row: 1 / 4;
    }
  }
`;
