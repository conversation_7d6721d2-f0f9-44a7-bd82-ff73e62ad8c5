"use client";
import { useEffect, useMemo, useRef, useState } from "react";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { useTranslations } from "next-intl";
import { CardWrapper, Title } from "@/components/extra/Common";
import Dropdown from "@/components/ui/dropdown";
import Table from "@/components/ui/table";
import { formatNumber } from "@/lib/number";
import { motion } from "framer-motion";
import useComponentSize from "@/hooks/useComponentSize";
import Select from "@/components/ui/Select/select";
import { convertWeight } from "@/lib/unit-converter";
import { useUserStore } from "@/store/userStore";
import Switch from "@/components/ui/Select/switch";
import NoData from "@/components/ui/no-data";

ChartJS.register(ArcElement, Tooltip, Legend);

const getRandomColor = () => {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  return `rgba(${r}, ${g}, ${b}, 0.7)`;
};

const division = ["count", "weight"];

const groupByTypeAsArray = (list: EquipmentListItemType[]) => {
  const groupedMap = new Map<string, EquipmentListItemType[]>();

  list.forEach((item) => {
    if (!groupedMap.has(item.type)) {
      groupedMap.set(item.type, []);
    }
    groupedMap.get(item.type)!.push(item);
  });

  return Array.from(groupedMap.entries()).map(([type, items]) => ({
    type,
    items,
  }));
};

interface IEquipmentDonutChart {
  items: EquipmentListItemType[];
}

const EquipmentDonutChart = ({ items: _items }: IEquipmentDonutChart) => {
  const t = useTranslations("Equipment.Chart");
  const [key, setKey] = useState<"count" | "weight">("count");
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const ref = useRef<HTMLDivElement>(null);
  const { width } = useComponentSize(ref);

  const user = useUserStore((state) => state.user);
  const unit: WeightUnit = user?.weightUnit || "g";

  const divisionDropdownOption = useMemo(
    () => division.map((div) => ({ label: t(div), value: div })),
    []
  );

  const items = useMemo(
    () =>
      _items.map((item) => ({
        ...item,
        weight: convertWeight(item.weight, item.unit, unit),
        unit,
      })),
    [_items, unit]
  );
  useEffect(() => {
    if (selectedType && _items.length === 0) {
      setSelectedType(null);
    }
  }, [_items.length]);

  const groupedList = useMemo(() => groupByTypeAsArray(items), [items]);

  const labels = useMemo(() => groupedList.map((item) => item.type), [items]);
  const data = useMemo(
    () =>
      groupedList.map(({ items }) =>
        items.reduce(
          (prev, curr) =>
            prev + (key === "count" ? curr[key] : curr[key] * curr["count"]),
          0
        )
      ),
    [items, key]
  );

  const backgroundColor = useMemo(
    () => groupedList.map(() => getRandomColor()),
    [groupedList.length]
  );

  const options = useMemo(
    () => ({
      cutout: "80%",
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          callbacks: {
            label: function (tooltipItem: any) {
              const value = tooltipItem.raw;
              return `${formatNumber(value)}${key === "weight" ? ` ${unit}` : ""}`;
            },
          },
        },
      },
      onClick: (event: any, elements: any) => {
        if (elements.length > 0) {
          const index = elements[0].index;
          const clickedType = labels[index];

          // Toggle table visibility
          setSelectedType((prev) =>
            prev === clickedType ? null : clickedType
          );
        }
      },
      maintainAspectRatio: false, // Allow resizing dynamically
    }),
    [unit, key, labels]
  );

  const chartProps = {
    data: {
      labels,
      datasets: [
        {
          data,
          backgroundColor,
        },
      ],
    },
    options,
  };

  // Get the selected type's items
  const selectedItems = useMemo(() => {
    if (!selectedType) return [];
    return (
      groupedList
        .find((group) => group.type === selectedType)
        ?.items.map((item) => ({
          ...item,
          weight: `${item.weight}${item.unit}`,
        })) || []
    );
  }, [selectedType, groupedList, items]);
  return (
    <>
      {/* Doughnut Chart */}
      <>
        <Title className="text-xl font-bold">{t("title")}</Title>
        <div className="my-2">
          <Switch
            options={divisionDropdownOption}
            defaultValue={key}
            onChange={setKey as any}
          />
        </div>
        <div ref={ref} className="flex relative">
          {_items.length === 0 && (
            <div className="absolute w-full">
              <NoData />
            </div>
          )}
          <motion.div
            initial={{ width: "100%" }}
            animate={{ width: selectedType ? "50%" : "100%" }}
            transition={{ duration: 0.4 }}
            className="p-5"
          >
            <Doughnut {...chartProps} />
          </motion.div>

          {/* Dynamic Table Appears When a Section is Clicked */}
          {selectedType && (
            <motion.div
              initial={{ opacity: 0, y: 20, x: (-1 * width) / 2 }}
              animate={{ opacity: 1, y: 0, x: 0 }}
              transition={{ duration: 0.4 }}
              className="w-[50%]"
            >
              <Title className="text-xl font-bold text-center">
                {selectedType} {t("details")}
              </Title>
              <Table
                name="EquipmentTable"
                columns={[
                  { label: t("name"), value: "name" },
                  { label: t("weight"), value: "weight" },
                  { label: t("count"), value: "count" },
                ]}
                items={selectedItems}
              />
            </motion.div>
          )}
        </div>
      </>
    </>
  );
};

export default EquipmentDonutChart;
