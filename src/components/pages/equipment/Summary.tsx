import { CardWrapper, Title } from "@/components/extra/Common";
import TextEditor from "@/components/forms/text-editor";
import { formatNumber } from "@/lib/number";
import { convertWeight } from "@/lib/unit-converter";
import { useUserStore } from "@/store/userStore";
import { useTranslations } from "next-intl";
import { useMemo } from "react";

const EquipmentSummary = ({ items }: { items: EquipmentListItemType[] }) => {
  const t = useTranslations("Equipment.Summary");
  const user = useUserStore((state) => state.user);
  const unit: WeightUnit = user?.weightUnit || "g";

  const totalWeight = useMemo(
    () =>
      items.reduce(
        (prev, curr) =>
          prev + +convertWeight(curr.weight, curr.unit, unit) * curr.count,
        0
      ),
    [items, unit]
  );
  const totalCount = useMemo(
    () => items.reduce((prev, curr) => prev + +curr.count, 0),
    [items]
  );

  return (
    <>
      <Title className="text-xl font-bold">{t("title")}</Title>

      <div className="mt-2">
        <div className="flex justify-between py-3">
          <label>{t("weight")}</label>
          <b className="text-lg">
            {formatNumber(totalWeight)}
            &nbsp;
            {unit}
          </b>
        </div>
        <hr />
        <div className="flex justify-between py-3">
          <label>{t("count")}</label>
          <b className="text-lg">
            {formatNumber(totalCount)}
          </b>
        </div>
        <hr />
      </div>
    </>
  );
};

export default EquipmentSummary;
