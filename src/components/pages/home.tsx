"use client";
import { useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH, weatherIcons } from "@/lib/constants";
import { deepEqual } from "@/lib/object";
import { getLocationName, getWeather } from "@/services/weatherService";
import { addDays, format } from "date-fns";
import { enUS, ko } from "date-fns/locale";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import Button from "../forms/button";

interface LatLon {
  latitude: number;
  longitude: number;
}

const SEOUL: LatLon = {
  latitude: 37.5326,
  longitude: 127.024612,
};
const NEW_YORK: LatLon = {
  latitude: 40.66436,
  longitude: -73.9385,
};

const Home = () => {
  const t = useTranslations("Home");
  const locale = useLocale();
  const router = useRouter();
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
  }>();
  const [name, setName] = useState<string>();
  const [weatherData, setWeatherData] = useState<WeatherInterval[] | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const weekDays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
  const getData = async () => {
    if (location) {
      try {
        const { latitude, longitude } = location;
        const body: WeatherRequestParams = {
          location: `${latitude},${longitude}`,
          units: "metric",
          fields: [
            "temperatureAvg",
            "temperatureMax",
            "temperatureMin",
            "windSpeedAvg",
            "cloudCoverAvg",
            "precipitationProbabilityAvg",
            "sunriseTime",
            "sunsetTime",
            "weatherCode",
          ],
          timesteps: ["1d"],
        };
        const data: WeatherResponse = await getWeather(body);
        // const data: WeatherResponse = { ...mockData }

        if (data && data?.data) {
          const dailyForecast = data.data.timelines[0]?.intervals || [];
          setWeatherData(dailyForecast);
        } else {
          const next = locale === "en" ? { ...NEW_YORK } : { ...SEOUL };
          if (!deepEqual(next, location)) {
            setLocation(next);
          }
          setError("Failed to fetch weather data.");
        }
        const loc = await getLocationName({
          ...location,
          locale: locale as any,
        });
        const name = `${loc.address.city || loc.address.town || loc.address.village}, ${loc.address.country}`;
        setName(name);
      } catch (e) {
        console.error(e);
        setError("Error");
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    if ("geolocation" in navigator) {
      // Retrieve latitude & longitude coordinates from `navigator.geolocation` Web API
      navigator.geolocation.getCurrentPosition(({ coords }) => {
        const { latitude, longitude } = coords;
        setLocation({ latitude, longitude });
      });
    }
  }, []);

  useEffect(() => {
    getData();
  }, [location]);

  return (
    <div className="top">
      <div className="text-center py-[90px] flex flex-wrap md:flex-nowrap">
        <div className="w-full flex flex-col items-start justify-center">
          <div className="mb-5 text-left">
            <h1
              className="text-[30px] md:text-[50px]"
              style={{ fontFamily: "OA Gothic", fontWeight: 900 }}
            >
              {t("title")}
            </h1>
            <p>
              <h4>{t("head-introduction")}</h4>
              {t("introduction")}
            </p>
          </div>
          <Button
            onClick={() => router.push(FRONT_URL_PATH.GEAR_MEASUREMENT)}
            $radius="25px"
            $minWidth="200px"
            $theme="black"
          >
            무게측정하기
          </Button>
        </div>
        <div className="w-full py-[50px] md:py-0">
          <img
            className="md:w-[400px] md:ml-[auto]"
            src="/main-image.png"
          ></img>
        </div>
      </div>

      <div className="bottom">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-xl font-bold">{t("weather_forecast")}</h2>
          <span className="text-sm text-gray-500">{name}</span>
        </div>

        <div className="flex flex-wrap md:flex-nowrap justify-between gap-3">
          {(
            weatherData ||
            Array.from({ length: 6 }, (_, i) => ({
              startTime: addDays(new Date(), i),
            }))
          ).map((day: any, index) => {
            const isToday =
              !loading &&
              format(new Date(day.startTime), "EEE", {
                locale: locale === "ko" ? ko : enUS,
              }) ===
                format(new Date(), "EEE", {
                  locale: locale === "ko" ? ko : enUS,
                });

            const weatherCode = loading ? null : day.values?.weatherCode;
            const weatherIcon = weatherIcons[weatherCode] || "❓"; // Default if no match

            return (
              <div
                key={index}
                className="bg-gray-100 w-[45%] md:w-full shadow rounded-2xl p-3 flex flex-col items-center min-h-[180px]"
              >
                {/* Day and Date */}
                <div className="text-sm font-semibold">
                  {loading ? (
                    <div className="h-4 w-8 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    <>
                      {t(weekDays[new Date(day.startTime).getDay()])}
                      {isToday && (
                        <span className="ml-1 text-xs bg-gray-700 p-1 rounded text-white">
                          {t("today")}
                        </span>
                      )}
                    </>
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  {loading ? (
                    <div className="h-3 w-10 bg-gray-300 rounded animate-pulse mt-1" />
                  ) : (
                    format(new Date(day.startTime), "M.d")
                  )}
                </div>

                {/* Weather Icon */}
                <div className="my-2 text-2xl">
                  {loading ? (
                    <div className="h-10 w-10 bg-gray-300 rounded-full animate-pulse" />
                  ) : (
                    weatherIcon
                  )}
                </div>

                {/* Rain Chance & Temp */}
                <div className="flex items-center space-x-1">
                  {loading ? (
                    <div className="h-4 w-12 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    <>
                      <span className="text-blue-500 text-xs">
                        {day.values?.precipitationProbabilityAvg}%
                      </span>
                      <span className="text-blue-500 text-xs">
                        {day.values?.precipitationProbabilityAvg}%
                      </span>
                    </>
                  )}
                </div>
                <div className="flex items-center space-x-1 text-sm">
                  {loading ? (
                    <div className="h-4 w-14 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    <>
                      <span className="text-blue-500">
                        {day.values?.temperatureMin}°
                      </span>
                      <span className="text-red-500">
                        {day.values?.temperatureMax}°
                      </span>
                    </>
                  )}
                </div>

                {/* Wind Speed */}
                <div className="text-xs text-gray-500">
                  {loading ? (
                    <div className="h-3 w-16 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    `${t("wind")}: ${day.values?.windSpeedAvg} km/h`
                  )}
                </div>

                {/* Sunrise & Sunset */}
                <div className="text-xs text-gray-500 flex flex-col mt-1">
                  {loading ? (
                    <div className="h-3 w-12 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    <>
                      <span>
                        {t("sunrise")}:{" "}
                        {day.values?.sunriseTime &&
                          format(new Date(day.values?.sunriseTime), "HH:mm")}
                      </span>
                      <span>
                        {t("sunset")}:{" "}
                        {day.values?.sunsetTime &&
                          format(new Date(day.values?.sunsetTime), "HH:mm")}
                      </span>
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Home;
