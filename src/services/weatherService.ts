import { stringify } from "querystring";

export const getWeather = async (
  params: WeatherRequestParams
): Promise<WeatherResponse> => {
  const apikey = process.env.NEXT_PUBLIC_TOMORROW_API_KEY || "";
  const url = `https://api.tomorrow.io/v4/timelines?${stringify({ apikey })}`;
  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify({ ...params }),
  });
  const data: WeatherResponse = await response.json();
  return data;
};

export const getLocationName = async (
  params: {
    latitude?: number;
    longitude?: number;
    locale?:
      | "en" // English
      | "ko" // Korean
      | "fr" //French
      | "es" // Spanish
      | "de" //German
      | "zh" // Chinese
      | "ja"; // Japanese
  } = {
    locale: "en",
  }
) => {
  const query = {
    lat: params.latitude,
    lon: params.longitude,
    "accept-language": params.locale,
    format: "json",
  };
  const url = `https://nominatim.openstreetmap.org/reverse?${stringify({ ...query })}`;
  const response = await fetch(url);
  const data = await response.json();
  return data;
};
