import api from "@/lib/api";
import { stringify } from "querystring";

export const getPostItem = async ({
  postId,
  ...params
}: { postId: number } & PostItemRequestType): Promise<PostItemType | null> => {
  try {
    const data = await api.get<PostItemType>(
      `/v1/posts/${postId}?${stringify(params)}`
    );
    return data;
  } catch (e) {
    return null;
  }
};

export const getPosts = async (
  params: PostListRequestType
): Promise<PostListResponseType> => {
  const data = await api.get<PostListResponseType>(
    `/v1/posts?${stringify(params)}`
  );
  return data;
};

export const getPostById = async (
  params: PostItemRequestType
): Promise<PostItemType> => {
  const { postId, locale } = params;
  const data = await api.get<PostItemType>(
    `/v1/posts/${postId}?locale=${locale}`
  );
  return data;
};

export const createPost = async (params: CreatePostType): Promise<any> => {
  const data = await api.post<CreatePostType>(`/v1/posts?`, params);
  return data;
};

export const updatePost = async (params: CreatePostType): Promise<any> => {
  const data = await api.post<CreatePostType>(
    `/v1/posts/${params.postId}?`,
    params
  );
  return data;
};

export const toggleLike = async (postId: number): Promise<boolean> => {
  const { like } = await api.post<{ like: boolean }>(
    `/v1/posts/${postId}/likes`
  );
  return like;
};

export const getPostCategories = async (
  params: PostCategoryListRequestType
): Promise<PostCategoryListResponseType> => {
  const data = await api.get<PostCategoryListResponseType>(
    `/v1/posts/categories?${stringify(params)}`
  );
  return data;
};

export const deletePost = async (id: number): Promise<any> => {
  const data = await api.delete(`/v1/posts/${id}`);
  return data;
};

export const updatePostViewCnt = async (id: number): Promise<void> => {
  await api.post(`/v1/posts/viewCnt/${id}`);
};
