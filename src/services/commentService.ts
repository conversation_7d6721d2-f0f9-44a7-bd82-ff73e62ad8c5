import api from "@/lib/api";
import { stringify } from "querystring";

export const getCommentsByPostId = async (
  postId: number
): Promise<CommentListResponse> => {
  const data = await api.get<CommentListResponse>(
    `/v1/comments?postId=${postId}`
  );
  return data;
};

export const createComment = async (
  params: CreateCommentRequest
): Promise<any> => {
  await api.post(`/v1/comments`, params);
};

export const updateComment = async (
  params: UpdateCommentRequest
): Promise<any> => {
  await api.post(`/v1/comments/${params.commentId}`);
};

export const deleteComment = async (
  params: DeleteCommentRequest
): Promise<any> => {
  await api.delete(`/v1/comments/${params.commentId}`);
};

export const toggleCommentLike = async (
  commentId: number
): Promise<boolean> => {
  const { like } = await api.post<{ like: boolean }>(
    `/v1/comments/${commentId}/likes`
  );
  return like;
};
