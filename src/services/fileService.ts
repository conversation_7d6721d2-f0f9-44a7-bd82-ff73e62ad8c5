import api from "@/lib/api";

/**
 * Uploads files to the server.
 *
 * @param {File[]} files - The list of files to upload.
 * @returns {Promise<FileResponseType>} - The response containing file IDs and details.
 */
export const uploadFiles = async (
  files: File[],
  fileId?: string | null
): Promise<FileResponse> => {
  try {
    // Create FormData
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    // Make API call
    const response = await api.post<FileResponse>(
      `/v1/files${fileId ? `/${fileId}` : ""}`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    return response;
  } catch (error) {
    console.error("File upload failed", error);
    throw new Error("File upload failed");
  }
};
