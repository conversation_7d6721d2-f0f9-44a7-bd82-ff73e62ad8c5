import api from "@/lib/api";
import { stringify } from "querystring";

export const createHistory = async (
  params: HistoryCreateRequestType
): Promise<void> => {
  return await api.post<void>(`/v1/histories`, { ...params });
};

export const getMyHistories = async (
  params: HistoryListRequestType
): Promise<HistoryListResponseType> => {
  return await api.get<HistoryListResponseType>(
    `/v1/histories?${stringify({ ...params })}`
  );
};

export const getHistory = async ({
  historyId,
  ...params
}: HistoryItemRequestType): Promise<HistoryItemResponseType | null> => {
  try {
    return await api.get<HistoryItemResponseType>(
      `/v1/histories/${historyId}?${stringify({ ...params })}`
    );
  } catch (e) {
    console.error(e);
    return null;
  }
};
