import api from "@/lib/api";
import { stringify } from "querystring";

export const getMyEquipmentList = async (
  params: EquipmentListRequestType
): Promise<EquipmentListResponseType> => {
  const data = await api.get<EquipmentListResponseType>(
    `/v1/equipment?${stringify(params)}`
  );
  return data;
};

export const saveMyEquipment = async (
  params: EquipmentSaveRequestType
): Promise<void> => {
  await api.post<EquipmentListResponseType>(`/v1/equipment`, params);
};

export const updateMyEquipment = async ({
  userEquipmentId,
  ...params
}: EquipmentUpdateRequestType): Promise<void> => {
  await api.put<void>(`/v1/equipment/${userEquipmentId}`, params);
};

export const deleteMyEquipment = async (
  userEquipmentId: number
): Promise<void> => {
  await api.delete<void>(`/v1/equipment/${userEquipmentId}`);
};

export const getEquipmentTypeList = async (
  params: EquipmentListRequestType
): Promise<ListResponseType<LabelValueType>> => {
  const data = await api.get<ListResponseType<LabelValueType>>(
    `/v1/equipment/types?${stringify(params)}`
  );
  return data;
};

export const getUserEquipmentPresetList = async (
  params: UserEquipmentPresetListRequestType
): Promise<UserEquipmentPresetListResponseType> => {
  const data = await api.get<UserEquipmentPresetListResponseType>(
    `/v1/equipment/preset?${stringify(params)}`
  );
  return data;
};

export const getUserEquipmentPresetItem = async (
  params: UserEquipmentPresetRequestType
): Promise<UserEquipmentPresetItemType> => {
  const data = await api.get<UserEquipmentPresetItemType>(
    `/v1/equipment/preset/${params.userEquipmentSetId}?${stringify(params)}`
  );
  return data;
};
export const createUserEquipmentPresetItem = async (
  params: UserEquipmentPresetCreate
): Promise<UserEquipmentPresetSaveResponse> => {
  return await api.post<UserEquipmentPresetSaveResponse>(
    `/v1/equipment/preset`,
    { ...params }
  );
};
export const updateUserEquipmentPresetItem = async (
  params: UserEquipmentPresetUpdate
): Promise<UserEquipmentPresetSaveResponse> => {
  return await api.put<UserEquipmentPresetSaveResponse>(
    `/v1/equipment/preset/${params.userEquipmentSetId}`,
    {
      ...params,
    }
  );
};

export const deleteUserEquipmentPresetItem = async (
  userEquipmentSetId: number
): Promise<void> => {
  await api.delete<UserEquipmentPresetSaveResponse>(
    `/v1/equipment/preset/${userEquipmentSetId}`
  );
};

export const changeUserEquipmentPresetOrders = async (
  params: UserEquipmentPresetChangeOrderRequestType
): Promise<void> => {
  await api.put<void>(`/v1/equipment/preset/orders`, params);
};

export const getEquipmentFavItemList = async (
  params: EquipmentListRequestType
): Promise<EquipmentFavItemResponseType> => {
  const data = await api.get<EquipmentFavItemResponseType>(
    `/v1/equipment/favorites?${stringify(params)}`
  );
  return data;
};
