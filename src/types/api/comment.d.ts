type CommentType = {
  postId: number;
  commentId: number;
  upperCommentId?: string;
  userId: number;
  name: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  replies: CommentType[];

  likeCnt: number;
  likeYn: boolean;
  commentCnt?: number;
  userProfileImage?: FileType;
};

type CommentListResponse = ListResponseType<CommentType>;

type CreateCommentRequest = {
  postId?: number;
  upperCommentId?: number;
  usrId: number;
  cntnt: string;
};

type UpdateCommentRequest = {
  commentId: number;
  usrId: number;
  cntnt: string;
};

type DeleteCommentRequest = {
  commentId: number;
};
