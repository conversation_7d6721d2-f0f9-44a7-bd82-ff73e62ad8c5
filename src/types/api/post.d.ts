type PostItemRequestType = {
  locale: string;
  postId: number;
};

type PostListRequestType = {
  locale: string;
  title: string;
  postCategoryId?: string;
  page: number;
  size: number;
  athrId?: number;
};

type PostListType = {
  postId: number;
  authorName: string;
  authorIdName: string;
  title: string;
  postCategoryId: string;
  content: string;
  createdAt: string;
  thumbnailId?: string;
  thumbnailNum?: number;
  userProfileImage?: FileType;
  viewCnt: number;
  likeCnt: number;
  commentCnt: number;
};

type PostListResponseType = ListResponseType<PostListType>;

type PostItemType = {
  postId: number;
  authorName: string;
  authorIdName: string;
  title: string;
  postCategoryId: string;
  content: string;
  createdAt: string;
  thumbnailId?: string;
  thumbnailNum?: number;
  userProfileImage?: FileType;
  category?: string;
  likeYn: boolean;

  viewCnt: number;
  likeCnt: number;
  commentCnt: number;
};

type CreatePostType = {
  title: string;
  content: string;
  postCategoryId: string;
  thumbnailId?: string;
  thumbnailNum?: number;
  postId?: number;
};

type PostCategoryListRequestType = {
  locale: string;
};

type PostCategoryListResponseType = ListResponseType<LabelValueType>;
