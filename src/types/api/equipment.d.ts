type EquipmentListRequestType = {
  locale: string;
};
// 장비리스트
type EquipmentListItemType = {
  userEquipmentId: number;
  brandName: string;
  name: string;
  equipmentTypeId?: string;
  type: string;
  weight: number;
  unit: WeightUnit;
  count: number;
};

// 장비등록
type EquipmentSaveRequestType = {
  brandName: string;
  name: string;
  equipmentTypeId?: string;
  weight: number;
  unit: WeightUnit;
  count: number;
};
// 장비등록
type EquipmentUpdateRequestType = {
  userEquipmentId: number;
  brandName: string;
  name: string;
  equipmentTypeId?: string;
  weight: number;
  unit: WeightUnit;
  count: number;
};

// 장비리스트
type EquipmentListResponseType = ListResponseType<EquipmentListItemType>;

// 장비 프리셋 리스트
// 장비 프리셋 상세 타입
type UserEquipmentPresetItemType = {
  userEquipmentSetId?: number;
  title: string;
  items: EquipmentListItemType[];
};

type UserEquipmentPresetListRequestType = {
  locale: string;
};
type UserEquipmentPresetListResponseType =
  ListResponseType<UserEquipmentPresetItemType>;
// 장비 프리셋상세 요청
type UserEquipmentPresetRequestType = {
  userEquipmentSetId: number;
  locale: string;
};

// 장비 프리셋 저장
type UserEquipmentPresetCreate = {
  title: string;
  items: EquipmentListItemType[];
  order: number;
};

// 장비 프리셋 업데이트
type UserEquipmentPresetUpdate = UserEquipmentPresetCreate & {
  userEquipmentSetId: number;
};
type UserEquipmentPresetSaveResponse = {
  userEquipmentSetId: number;
};

// 장비 프리셋 저장
type UserEquipmentPresetChangeOrderItemType = {
  userEquipmentSetId: number;
  order: number;
};
type UserEquipmentPresetChangeOrderRequestType = {
  items: UserEquipmentPresetChangeOrderItemType[];
};

// 즐겨찾기 장비리스트
// 장비리스트
type EquipmentFavItemType = {
  equipmentFavItemId: number;
  brandName: string;
  name: string;
  equipmentTypeId?: string;
  weight: number;
  unit: WeightUnit;
  count: number;
};

type EquipmentFavItemResponseType = ListResponseType<EquipmentFavItemType>;
