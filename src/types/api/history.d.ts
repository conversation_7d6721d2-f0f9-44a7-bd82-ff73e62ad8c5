type HistoryCreateRequestType = Partial<HistoryCreateType> & {
  details: HistoryDetailCreateType[];
};

/**
 * 히스토리 생성
 */
type HistoryCreateType = {
  startAt: string;
  endAt: string;
  name: string;
  location: string;
  distance: number;
  unit: DistanceUnit;
};

/**
 * 히스토리 생성 > 히스토리 상세
 */
type HistoryDetailCreateType = {
  brandName: string;
  name: string;
  equipmentTypeId?: string;
  weight: number;
  unit: WeightUnit;
  count: number;
};

/**
 * 히스토리 목록타입
 */
type HistoryListItemType = HistoryCreateType & {
  historyId: number;
  createdAt: Date;
};

/**
 * 히스토리 개별
 */
type HistoryItemType = HistoryCreateType & {
  historyId: number;
  createdAt: Date;
  details: HistoryDetailItemType[];
};

/**
 * 히스토리 개별 > 히스토리 상세타입
 */
type HistoryDetailItemType = HistoryCreateType & {
  historyDetailId: number;
  type: string;
};

/**
 * 히스토리 목록조회 요청
 */
type HistoryListRequestType = {
  page: number;
  size: number;
};
/**
 * 히스토리 목록조회 응답
 */
type HistoryListResponseType = ListResponseType<HistoryListItemType>;
/**
 * 히스토리 개별조회 요청
 */
type HistoryItemRequestType = {
  locale: string;
  historyId: number;
};
/**
 * 히스토리 개별조회 응답
 */
type HistoryItemResponseType = HistoryItemType;
