interface InputRef {
  focus: () => void;
  clear: () => void;
}

interface TextareaRef {
  focus: () => void;
  clear: () => void;
}

interface DateTimeInputRef {
  clear: () => void;
}

interface DateTimeRangeRef {
  clear: () => void;
}

interface TextEditorRef {
  getContents: () => string | undefined;
  getFile: () => FileType | undefined;
  setContents: (content: string) => void;
  // getFileId: () => string | undefined;
  // getFileSn: () => number | undefined;
}
