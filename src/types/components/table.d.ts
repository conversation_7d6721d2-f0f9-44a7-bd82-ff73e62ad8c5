type TableColumnType = LabelValueType & {
  style?: any;
};
// interface ITable {
//   columns: TableColumnType[];
//   items: any[];
//   name?: string;
//   onClick?: any;
// }
interface ITable {
  columns: TableColumnType[];
  items: Record<string, any>[];
  name?: string;
  onClick?: (item: Record<string, any>) => void;
  checkable?: boolean;
  className?: string;
}

interface TableRef {
  getCheckedList: () => Record<string, any>[]; // Returns selected items
}
