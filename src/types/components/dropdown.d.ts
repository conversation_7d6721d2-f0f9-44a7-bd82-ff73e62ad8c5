type DropDownOptionType = LabelValueType & {
  onClick?: (item: any) => void;
};
interface DropDownProps {
  options: DropDownOptionType[];
  value?: string | number;
  defaultValue?: string | number;
  children?: React.ReactNode;
  onChange?: (value) => void;

  $dropdownAlign?: "right" | "left";
  $dropdownWidth?: string;
  $minWidth?: string;
  $padding?: string;
  fontSize?: string;
  $margin?: string;
  $theme?: string;
}
