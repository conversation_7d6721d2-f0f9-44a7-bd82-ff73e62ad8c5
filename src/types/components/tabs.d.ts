type TabType<T = any> = {
  id?: string;
  label: string | React.ReactNode;
  value: T;
  content: React.ReactNode;
};

interface ITabs {
  tabs: TabType[];
  onClickAdd?: () => void;
  onChange?: (activeIndex: number, tabList?: any[]) => void;
}

interface TabsRef {
  setTabs: (tabs: TabType[]) => void;
  getTabs: () => TabType[];
  setActiveTab: (index: number) => void;
  getActiveTab: () => number;
}
