/** Weather Request Params */
interface WeatherRequestParams {
  location: string;
  fields: string[];
  timesteps: ("current" | "1h" | "1d")[];
  units?: "metric" | "imperial";
  timezone?: string;
}
interface WeatherInterval {
  startTime: string;
  values: {
    temperatureAvg: number;
    temperatureMax: number;
    temperatureMin: number;
    windSpeedAvg: number;
    cloudCoverAvg: number;
    precipitationProbabilityAvg: number;
    sunriseTime: string;
    sunsetTime: string;
    weatherCode: string;
  };
}

interface WeatherResponse {
  data: {
    timelines: {
      timestep: string;
      intervals: WeatherInterval[];
    }[];
  };
}
