# 설정

## `next.config.ts` 에 플러그인 래핑

```typescript
// next.config.ts
import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  /* config options here */
  compiler: {
    styledComponents: true, // styled-components 활성화
  },
};

export default withNextIntl(nextConfig);
```

## 설정 파일 추가

- `@/middleware.ts`
- `@/i18n/locale-map.ts`
- `@/i18n/request.ts`
- `@/i18n/routing.ts`
- `@/lib/messages/en.json`
- `@/lib/messages/ko.json`

`@/lib/messages/[en|ko].json` 경로변경시 `@/i18n/request.ts` 확인

## `[en|ko].json`에 문구추가

# 사용방법

## 메세지

```json
// en.json
{
  "Components": {
    "Calendar": {
      "selectedDate": "Selected Date"
    }
  }
}
```

```typescript
import { useTranslations } from "next-intl";

const t = useTranslations("Components.Calendar");
// ...
<div> { t("selectedDate"); } </div>
//...
```

## 라우팅

`<Link />`, `useRouter()`, `usePathname()` `redirect()`, `getPathnmae()` 의 함수는 `@/i18n/routing`것 사용
