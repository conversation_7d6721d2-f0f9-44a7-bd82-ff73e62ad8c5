import EquipmentPresetList from "@/components/pages/equipment/presets";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getMyEquipmentList } from "@/services/equipmentService";
import { Metadata, ResolvingMetadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
// import { useLocale } from "next-intl";

export default async function GearPresetPage() {
  const locale = await getLocale();
  let items: EquipmentListItemType[];
  try {
    const locale = await getLocale();
    const res = await getMyEquipmentList({ locale });
    items = res.items;
  } catch (e) {
    items = [];
  }
  return (
    <main className="">
      <EquipmentPresetList equipmentList={items} />
    </main>
  );
}

export async function generateMetadata(
  {}: {},
  parent: ResolvingMetadata
): Promise<Metadata> {  
  const t = await getTranslations("Preset");
  const locale = await getLocale();

  const previousImages = (await parent).openGraph?.images || [];
  const images = [...previousImages];

  const title = t("title"); 
  const description = t("description");
  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}${FRONT_URL_PATH.GEAR_PRESET}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: "website",
      siteName: "Hike A Good Day",
      url,
      locale, // or ko_KR
      images,
    },
    alternates: {
      canonical: `/en${FRONT_URL_PATH.GEAR_PRESET}`,
      languages: {
        en: `/en${FRONT_URL_PATH.GEAR_PRESET}`,
        ko: `/ko${FRONT_URL_PATH.GEAR_PRESET}`,
      },
    },
  };
}