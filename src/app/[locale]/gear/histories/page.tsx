import EquipmentHistoryList from "@/components/pages/equipment/history/List";
import { FRONT_URL_PATH } from "@/lib/constants";
import { Metadata, ResolvingMetadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";

export default async function GearHistoryPage() {
  return (
    <main className="">
      <EquipmentHistoryList />
    </main>
  );
}

export async function generateMetadata(
  {}: {},
  parent: ResolvingMetadata
): Promise<Metadata> {  
  const t = await getTranslations("History");
  const locale = await getLocale();

  const previousImages = (await parent).openGraph?.images || [];
  const images = [...previousImages];

  const title = t("title"); 
  const description = t("description");
  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}${FRONT_URL_PATH.GEAR_HISTORY}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: "website",
      siteName: "Hike A Good Day",
      url,
      locale, // or ko_KR
      images,
    },
    alternates: {
      canonical: `/en${FRONT_URL_PATH.GEAR_HISTORY}`,
      languages: {
        en: `/en${FRONT_URL_PATH.GEAR_HISTORY}`,
        ko: `/ko${FRONT_URL_PATH.GEAR_HISTORY}`,
      },
    },
  };
}
