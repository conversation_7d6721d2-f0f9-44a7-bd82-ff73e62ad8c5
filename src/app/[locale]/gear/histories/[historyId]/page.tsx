import EquipmentHistory from "@/components/pages/equipment/history";
import NoData from "@/components/ui/no-data";
import { getHistory } from "@/services/historyService";
import { getLocale } from "next-intl/server";

type GearHistoryItemPageParamsType = Promise<{ historyId: number }>;
export default async function GearHistoryItemPage({
  params,
}: {
  params: GearHistoryItemPageParamsType;
}) {
  const { historyId } = await params;
  const locale = await getLocale();
  const item = await getHistory({ historyId, locale });
  if (!item) {
    return <NoData />;
  }
  return (
    <main className="">
      <EquipmentHistory item={item} />
    </main>
  );
}
