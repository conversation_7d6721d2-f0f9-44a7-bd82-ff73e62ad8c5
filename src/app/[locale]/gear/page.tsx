import Equipment from "@/components/pages/equipment";
import JsonLd from "@/components/seo/JsonLd";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getEquipmentTypeList } from "@/services/equipmentService";
import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import {
  generateMetadata as createMetadata,
  generateAlternateLanguages,
  createSEOUrl,
  defaultSEOConfig,
} from "@/lib/seo/metadata";
import {
  generateBreadcrumbJsonLd,
  generateWebApplicationJsonLd,
} from "@/lib/seo/jsonld";

export default async function WeightsPage() {
  const locale = await getLocale();
  const { items: typeOptions } = await getEquipmentTypeList({ locale });

  // Generate breadcrumb JSON-LD
  const breadcrumbJsonLd = generateBreadcrumbJsonLd({
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: createSEOUrl(defaultSEOConfig.baseUrl, locale, ""),
      },
      {
        "@type": "ListItem",
        position: 2,
        name: locale === "ko" ? "내 장비" : "My Gear",
      },
    ],
  });

  // Generate web application JSON-LD for gear management
  const webAppJsonLd = generateWebApplicationJsonLd({
    name: "Hike A Good Day - Gear Management",
    description:
      locale === "ko"
        ? "하이킹 장비 무게를 측정하고 관리하는 웹 애플리케이션"
        : "Web application for tracking and managing hiking gear weight",
    url: createSEOUrl(defaultSEOConfig.baseUrl, locale, FRONT_URL_PATH.GEAR),
    applicationCategory: "UtilitiesApplication",
    operatingSystem: "Web Browser",
  });

  return (
    <>
      <JsonLd data={breadcrumbJsonLd} />
      <JsonLd data={webAppJsonLd} />
      <main className="">
        <Equipment typeOptions={typeOptions} />
      </main>
    </>
  );
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("Equipment");
  const locale = await getLocale();

  const title = t("title");
  const description = t("description");
  const url = createSEOUrl(
    defaultSEOConfig.baseUrl,
    locale,
    FRONT_URL_PATH.GEAR
  );
  const alternateLanguages = generateAlternateLanguages(FRONT_URL_PATH.GEAR);

  return createMetadata({
    title,
    description,
    url,
    locale,
    type: "website",
    keywords:
      locale === "ko"
        ? ["장비", "무게측정", "하이킹장비", "등산장비", "배낭", "아웃도어"]
        : [
            "gear",
            "weight tracking",
            "hiking equipment",
            "backpacking gear",
            "outdoor equipment",
          ],
    canonical: `/en${FRONT_URL_PATH.GEAR}`,
    alternateLanguages,
  });
}
