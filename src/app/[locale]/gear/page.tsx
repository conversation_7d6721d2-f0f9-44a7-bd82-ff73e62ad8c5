import Equipment from "@/components/pages/equipment";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getEquipmentTypeList } from "@/services/equipmentService";
import { Metadata, ResolvingMetadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
// import { useLocale } from "next-intl";

export default async function WeightsPage() {
  const locale = await getLocale();
  const { items: typeOptions } = await getEquipmentTypeList({ locale });
  return (
    <main className="">
      <Equipment typeOptions={typeOptions} />
    </main>
  );
}


export async function generateMetadata(
  {}: {},
  parent: ResolvingMetadata
): Promise<Metadata> {  
  const t = await getTranslations("Equipment");
  const locale = await getLocale();

  const previousImages = (await parent).openGraph?.images || [];
  const images = [...previousImages];

  const title = t("title"); 
  const description = t("description");
  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}${FRONT_URL_PATH.GEAR}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: "website",
      siteName: "Hike A Good Day",
      url,
      locale, // or ko_KR
      images,
    },
    alternates: {
      canonical: `/en${FRONT_URL_PATH.GEAR}`,
      languages: {
        en: `/en${FRONT_URL_PATH.GEAR}`,
        ko: `/ko${FRONT_URL_PATH.GEAR}`,
      },
    },
  };
}
