import Measure from "@/components/pages/equipment/measure";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getEquipmentTypeList } from "@/services/equipmentService";
import { Metadata, ResolvingMetadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";

export default async function MeasurePage() {
  const locale = await getLocale();
  const { items: typeOptions } = await getEquipmentTypeList({ locale });
  return (
    <main className="">
      <Measure typeOptions={typeOptions} />
    </main>
  );
}


export async function generateMetadata(
  {}: {},
  parent: ResolvingMetadata
): Promise<Metadata> {
  const t = await getTranslations("Measure");
  const locale = await getLocale();

  const previousImages = (await parent).openGraph?.images || [];
  const images = [...previousImages];

  const title = t("title"); // 예: "My shoulder"
  const description = t("description"); // 예: "Record my journey"
  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}${FRONT_URL_PATH.GEAR_MEASUREMENT}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url,
      type: "website",
      locale,
      siteName: "Hike A Good Day",
      images,
    },
    alternates: {
      canonical: `/en/measure`,
      languages: {
        en: `/en/measure`,
        ko: `/ko/measure`,
      },
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
    },
  };
}