import SignUpForm from "@/components/pages/auth/sign-up-form";
import { redirect, useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";
import { Metadata, ResolvingMetadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import { cookies } from "next/headers";

export default async function SignUpPage() {
  const cookieStore = await cookies();
  const locale = await getLocale();
  const userStr = cookieStore.get("user")?.value;
  if (userStr) {
    const user = JSON.parse(userStr);
    if (user) {
      return redirect({ href: FRONT_URL_PATH.INDEX, locale });
    }
  }
  return <SignUpForm></SignUpForm>;
}

export async function generateMetadata(
  {}: {},
  parent: ResolvingMetadata
): Promise<Metadata> {
  const t = await getTranslations('Auth.Sign-up');
  const locale = await getLocale();

  const previousImages = (await parent).openGraph?.images || [];
  const images = [...previousImages];

  const title = t('title'); // e.g., "Sign In"
  const description = t('description') || t('title'); // Optional fallback

  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}/signin`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url,
      type: 'website',
      locale,
      siteName: 'Hike A Good Day',
      images,
    },
    twitter: {
      title,
      description,
    },
    alternates: {
      canonical: `/en/signup`,
      languages: {
        en: `/en/signup`,
        ko: `/ko/signup`,
      },
    },
  };
}