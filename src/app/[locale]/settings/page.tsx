import Settings from "@/components/pages/settings";
import { redirect } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";
import { getLocale } from "next-intl/server";
import { cookies } from "next/headers";

export default async function SuggestPage() {
  const cookieStore = await cookies();
  const locale = await getLocale();
  const userStr = cookieStore.get("user")?.value;
  if (userStr) {
    const user = JSON.parse(userStr);
    if (user) {
      return <main><Settings /></main>;
    }
  }
  return redirect({ href: FRONT_URL_PATH.INDEX, locale });
}
