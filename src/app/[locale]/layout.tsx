import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";
import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mon<PERSON> } from "next/font/google";
import "@/styles/globals.css";
import { SCProvider } from "@/components/layouts/SCProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Hike A Good Day",
    template: "%s | Hike A Good Day",
  },
  description: "Your lightweight hiking companion",
  openGraph: {
    title: "Hike A Good Day",
    description: "Your lightweight hiking companion",
    url: process.env.NEXT_PUBLIC_FRONT_URL,
    siteName: "Hike A Good Day",
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_FRONT_URL}/logo-l.png`,
        width: 1200,
        height: 630,
        alt: "Hike A Good Day",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Hike A Good Day",
    description: "Your lightweight hiking companion",
    images: [`${process.env.NEXT_PUBLIC_FRONT_URL}/logo-l.png`],
  },
};

export default async function RootLayout({
  modals,
  children,
  params,
}: Readonly<{
  modals: React.ReactNode;
  children: React.ReactNode;
  params: { locale: string };
}>) {
  const { locale } = await params;
  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }

  const messages = await getMessages();
  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        // style={{ background: "white" }}
      >
        <NextIntlClientProvider messages={messages} locale={locale}>
          <SCProvider>
            {modals}
            {children}
          </SCProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
