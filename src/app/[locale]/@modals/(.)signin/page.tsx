"use client";
import SignInForm from "@/components/pages/auth/sign-in-form";
import Modal from "@/components/ui/modal";
import { useUserStore } from "@/store/userStore";
import { useRouter } from "@/i18n/routing";
import { useEffect } from "react";
import { useTranslations } from "next-intl";

export default function SignInModal() {
  const t = useTranslations("Auth.Sign-in");
  const _prepared = useUserStore((state) => state._prepared);
  const user = useUserStore((state) => state.user);
  const router = useRouter();
  useEffect(() => {
    if (_prepared && user) {
      router.back();
    }
  }, [_prepared, user]);
  if (!_prepared) {
    return null;
  }
  if (_prepared && user) {
    return null;
  }
  return (
    <Modal title={t("title")} isPage>
      <SignInForm title=" " />
    </Modal>
  );
}
