import { CategoryTabs } from "@/components/pages/post/CategoryTabs";
import { getPostCategories } from "@/services/postService";
import { getLocale } from "next-intl/server";

export default async function PostLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const { items } = await getPostCategories({ locale });
  return (
    <>
      <PostWrapper>
        <div className="flex flex-col md:flex-row gap-4 relative">
          <div className="bg-white rounded-md md:w-[250px] h-fit">
            {/* 소메뉴 */}
            <CategoryTabs items={items} />
          </div>
          {children}
        </div>
      </PostWrapper>
    </>
  );
}

const PostWrapper = ({ children }: { children: React.ReactNode }) => {
  return <main className="p-0 md:p-3">{children}</main>;
};
