import Post from "@/components/pages/post";
import JsonLd from "@/components/seo/JsonLd";
import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import { FRONT_URL_PATH } from "@/lib/constants";
import {
  generateMetadata as createMetadata,
  generateAlternateLanguages,
  createSEOUrl,
  defaultSEOConfig,
} from "@/lib/seo/metadata";
import { generateBreadcrumbJsonLd } from "@/lib/seo/jsonld";

const ReviewPage = async () => {
  const locale = await getLocale();

  // Generate breadcrumb JSON-LD
  const breadcrumbJsonLd = generateBreadcrumbJsonLd({
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: createSEOUrl(defaultSEOConfig.baseUrl, locale, ""),
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Posts",
      },
    ],
  });

  return (
    <>
      <JsonLd data={breadcrumbJsonLd} />
      <Post />
    </>
  );
};

export default ReviewPage;

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("Posts");
  const locale = await getLocale();

  const title = t("title");
  const description = t("description");
  const url = createSEOUrl(
    defaultSEOConfig.baseUrl,
    locale,
    FRONT_URL_PATH.POSTS
  );
  const alternateLanguages = generateAlternateLanguages(FRONT_URL_PATH.POSTS);

  return createMetadata({
    title,
    description,
    url,
    locale,
    type: "website",
    keywords:
      locale === "ko"
        ? ["커뮤니티", "하이킹", "등산", "후기", "장비리뷰", "배낭"]
        : [
            "community",
            "hiking",
            "backpacking",
            "reviews",
            "gear reviews",
            "outdoor",
          ],
    canonical: `/en/posts`,
    alternateLanguages,
  });
}
