import Post from "@/components/pages/post";
import { Metadata, ResolvingMetadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import { FRONT_URL_PATH } from "@/lib/constants";

const ReviewPage = async () => {
  return <Post />;
};

export default ReviewPage;

export async function generateMetadata(
  {}: {},
  parent: ResolvingMetadata
): Promise<Metadata> {
  // read route params

  const t = await getTranslations("Posts");
  const locale = await getLocale();

  // optionally access and extend (rather than replace) parent metadata
  const previousImages = (await parent).openGraph?.images || [];
  const images = [...previousImages];

  const title = t("title");
  const description = t("description");
  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}${FRONT_URL_PATH.POSTS}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url,
      type: "website",
      locale,
      siteName: "Hike A Good Day",
      images,
    },
    alternates: {
      canonical: `/en/posts`,
      languages: {
        en: `/en/posts`,
        ko: `/ko/posts`,
      },
    },
  };
}
