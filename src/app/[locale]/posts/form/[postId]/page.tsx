import { PageWrapper } from "@/components/extra/Common";
import ReviewForm from "@/components/pages/post/form";
import { getPostById } from "@/services/postService";
import { getLocale } from "next-intl/server";

type ReviewFormUpdatePageParamsType = Promise<{ postId: number }>;
const ReviewFormUpdatePage = async ({
  params,
}: {
  params: ReviewFormUpdatePageParamsType;
}) => {
  const { postId } = await params;
  if (!postId) {
    return <div>잘못된 접근입니다. 게시글 ID가 필요합니다.</div>;
  }

  const locale = await getLocale();

  if (isNaN(postId)) {
    return <div>잘못된 게시글 ID입니다.</div>;
  }

  const body = { postId: postId, locale };

  try {
    const postData = await getPostById(body);

    return (
      <PageWrapper>
        <ReviewForm initialData={postData} />
      </PageWrapper>
    );
  } catch (error) {
    return <main>
      <div>게시글을 불러올 수 없습니다.</div>
    </main>
  }
};

export default ReviewFormUpdatePage;
