import { PageWrapper } from "@/components/extra/Common";
import PostItem from "@/components/pages/post/Item";
import { getLocale, getTranslations } from "next-intl/server";
import { getPostItem, updatePostViewCnt } from "@/services/postService";
import NoData from "@/components/ui/no-data";
import { Metadata, ResolvingMetadata } from "next";
import { getFileUrl } from "@/lib/file";
import { stripHtmlTags } from "@/lib/text";
import { FRONT_URL_PATH } from "@/lib/constants";

type ReviewItemPageParamsType = Promise<{ postId: number }>;
const ReviewItemPage = async ({
  params,
}: {
  params: ReviewItemPageParamsType;
}) => {
  const { postId } = await params;
  const locale = await getLocale();
  const item = await getPostItem({ postId, locale });
  if (!item) {
    return <NoData />;
  }
  await updatePostViewCnt(postId);
  return <PostItem item={item} />;
};

export default ReviewItemPage;

export async function generateMetadata(
  { params }: { params: Promise<{ postId: number }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  // read route params
  const t = await getTranslations("Posts");
  const { postId } = await params;

  const locale = await getLocale();
  const item = await getPostItem({ postId, locale });

  if (!item) {
    return {
      // TODO:
      title: t("not-found"),
    };
  }
  // optionally access and extend (rather than replace) parent metadata
  const previousImages = (await parent).openGraph?.images || [];
  const images = item.thumbnailId
    ? [
        getFileUrl({ fileId: item.thumbnailId, fileSn: item.thumbnailNum }),
        ...previousImages,
      ]
    : [...previousImages];

  const description = stripHtmlTags(item.content);
  const url = `${process.env.NEXT_PUBLIC_FRONT_URL}/${locale}${FRONT_URL_PATH.POSTS}/${item.postId}`;

  return {
    title: item.title,
    description,
    openGraph: {
      title: item.title,
      description,
      url,
      type: "article",
      locale,
      siteName: "Hike A Good Day",
      images,
      publishedTime: item.createdAt,
      authors: [item.authorName],
      tags: item.category ? [item.category] : [],
    },
    alternates: {
      canonical: `/en${FRONT_URL_PATH.POSTS}/${postId}`,
      languages: {
        en: `/en${FRONT_URL_PATH.POSTS}/${postId}`,
        ko: `/ko${FRONT_URL_PATH.POSTS}/${postId}`,
      },
    },
  };
}
