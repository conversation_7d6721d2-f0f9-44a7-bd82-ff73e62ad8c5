import { PageWrapper } from "@/components/extra/Common";
import PostItem from "@/components/pages/post/Item";
import JsonLd from "@/components/seo/JsonLd";
import { getLocale, getTranslations } from "next-intl/server";
import { getPostItem, updatePostViewCnt } from "@/services/postService";
import NoData from "@/components/ui/no-data";
import { Metadata } from "next";
import { getFileUrl } from "@/lib/file";
import { stripHtmlTags } from "@/lib/text";
import { FRONT_URL_PATH } from "@/lib/constants";
import {
  generateMetadata as createMetadata,
  generateAlternateLanguages,
  createSEOUrl,
  defaultSEOConfig,
  truncateDescription,
  generateKeywords,
} from "@/lib/seo/metadata";
import {
  generateArticleJsonLd,
  generateBreadcrumbJsonLd,
} from "@/lib/seo/jsonld";

type ReviewItemPageParamsType = Promise<{ postId: number }>;
const ReviewItemPage = async ({
  params,
}: {
  params: ReviewItemPageParamsType;
}) => {
  const { postId } = await params;
  const locale = await getLocale();
  const item = await getPostItem({ postId, locale });
  if (!item) {
    return <NoData />;
  }
  await updatePostViewCnt(postId);

  // Generate JSON-LD for the article
  const articleJsonLd = generateArticleJsonLd({
    headline: item.title,
    description: truncateDescription(stripHtmlTags(item.content)),
    image: item.thumbnailId
      ? [getFileUrl({ fileId: item.thumbnailId, fileSn: item.thumbnailNum })]
      : undefined,
    datePublished: item.createdAt,
    dateModified: item.createdAt, // Use createdAt as fallback since updatedAt might not exist
    author: {
      "@type": "Person",
      name: item.authorName,
    },
    publisher: {
      "@type": "Organization",
      name: "Hike A Good Day",
      logo: {
        "@type": "ImageObject",
        url: `${defaultSEOConfig.baseUrl}/logo-l.png`,
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": createSEOUrl(
        defaultSEOConfig.baseUrl,
        locale,
        `${FRONT_URL_PATH.POSTS}/${postId}`
      ),
    },
    url: createSEOUrl(
      defaultSEOConfig.baseUrl,
      locale,
      `${FRONT_URL_PATH.POSTS}/${postId}`
    ),
    articleSection: item.category || "Hiking",
    keywords: generateKeywords(item.content, ["hiking", "gear", "outdoor"], 8),
  });

  // Generate breadcrumb JSON-LD
  const breadcrumbJsonLd = generateBreadcrumbJsonLd({
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: createSEOUrl(defaultSEOConfig.baseUrl, locale, ""),
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Posts",
        item: createSEOUrl(
          defaultSEOConfig.baseUrl,
          locale,
          FRONT_URL_PATH.POSTS
        ),
      },
      {
        "@type": "ListItem",
        position: 3,
        name: item.title,
      },
    ],
  });

  return (
    <>
      <JsonLd data={articleJsonLd} />
      <JsonLd data={breadcrumbJsonLd} />
      <PostItem item={item} />
    </>
  );
};

export default ReviewItemPage;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ postId: number }>;
}): Promise<Metadata> {
  const t = await getTranslations("Posts");
  const { postId } = await params;
  const locale = await getLocale();
  const item = await getPostItem({ postId, locale });

  if (!item) {
    return {
      title: t("not-found"),
      description: t("not-found"),
    };
  }

  const description = truncateDescription(stripHtmlTags(item.content));
  const url = createSEOUrl(
    defaultSEOConfig.baseUrl,
    locale,
    `${FRONT_URL_PATH.POSTS}/${postId}`
  );
  const alternateLanguages = generateAlternateLanguages(
    `${FRONT_URL_PATH.POSTS}/${postId}`
  );

  const images = item.thumbnailId
    ? [
        {
          url: getFileUrl({
            fileId: item.thumbnailId,
            fileSn: item.thumbnailNum,
          }),
          width: 1200,
          height: 630,
          alt: item.title,
        },
      ]
    : undefined;

  const keywords = generateKeywords(
    item.content,
    ["hiking", "gear", "outdoor", item.category || ""].filter(Boolean),
    10
  );

  return createMetadata({
    title: item.title,
    description,
    url,
    locale,
    type: "article",
    images,
    publishedTime: item.createdAt,
    authors: [item.authorName],
    tags: item.category ? [item.category] : [],
    section: item.category || "Hiking",
    keywords,
    canonical: `/en${FRONT_URL_PATH.POSTS}/${postId}`,
    alternateLanguages,
  });
}
