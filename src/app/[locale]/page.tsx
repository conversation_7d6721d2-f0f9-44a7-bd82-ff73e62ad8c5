import Home from "@/components/pages/home";
import JsonLd from "@/components/seo/JsonLd";
import {
  generateMetadata,
  generateAlternateLanguages,
  createSEOUrl,
  defaultSEOConfig,
} from "@/lib/seo/metadata";
import {
  generateWebsiteJsonLd,
  getDefaultWebsiteJsonLd,
} from "@/lib/seo/jsonld";
import { NextPage } from "next";
import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations("Home");

  const title =
    locale === "ko"
      ? "Hike A Good Day - 당신의 가벼운 하이킹 동반자"
      : "Hike A Good Day - Your Lightweight Hiking Companion";
  const description =
    locale === "ko"
      ? "하이킹 장비 무게를 측정하고 관리하세요. 장비 프리셋, 무게 추적, 커뮤니티 리뷰까지 한 곳에서."
      : "Track and manage your hiking gear weight. Gear presets, weight tracking, and community reviews all in one place.";

  const url = createSEOUrl(defaultSEOConfig.baseUrl, locale, "");
  const alternateLanguages = generateAlternateLanguages("");

  return generateMetadata({
    title,
    description,
    url,
    locale,
    type: "website",
    keywords:
      locale === "ko"
        ? ["하이킹", "등산", "장비", "무게측정", "배낭", "아웃도어", "트레킹"]
        : [
            "hiking",
            "backpacking",
            "gear",
            "weight tracking",
            "outdoor",
            "trekking",
            "equipment",
          ],
    canonical: "/en",
    alternateLanguages,
  });
}

const HomePage: NextPage = async () => {
  const locale = await getLocale();
  const websiteJsonLd = generateWebsiteJsonLd(getDefaultWebsiteJsonLd(locale));

  return (
    <>
      <JsonLd data={websiteJsonLd} />
      <div
        style={{
          background: "linear-gradient(180deg, #F5FAF7 0%, #FFFFFF 100%)",
        }}
      >
        <main>
          <Home />
        </main>
      </div>
    </>
  );
};

export default HomePage;
