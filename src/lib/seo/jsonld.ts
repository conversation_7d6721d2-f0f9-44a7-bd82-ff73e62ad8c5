import { getFileUrl } from "@/lib/file";

export interface WebsiteJsonLd {
  name: string;
  description: string;
  url: string;
  logo?: string;
  sameAs?: string[];
  potentialAction?: {
    "@type": string;
    target: string;
    "query-input": string;
  };
}

export interface ArticleJsonLd {
  headline: string;
  description: string;
  image?: string[];
  datePublished: string;
  dateModified?: string;
  author: {
    "@type": "Person";
    name: string;
  };
  publisher: {
    "@type": "Organization";
    name: string;
    logo?: {
      "@type": "ImageObject";
      url: string;
    };
  };
  mainEntityOfPage: {
    "@type": "WebPage";
    "@id": string;
  };
  url: string;
  articleSection?: string;
  keywords?: string[];
}

export interface BreadcrumbJsonLd {
  itemListElement: Array<{
    "@type": "ListItem";
    position: number;
    name: string;
    item?: string;
  }>;
}

export interface WebApplicationJsonLd {
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem: string;
  offers?: {
    "@type": "Offer";
    price: string;
    priceCurrency: string;
  };
  aggregateRating?: {
    "@type": "AggregateRating";
    ratingValue: string;
    ratingCount: string;
  };
}

export function generateWebsiteJsonLd(data: WebsiteJsonLd): string {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: data.name,
    description: data.description,
    url: data.url,
    ...(data.logo && { logo: data.logo }),
    ...(data.sameAs && { sameAs: data.sameAs }),
    ...(data.potentialAction && { potentialAction: data.potentialAction }),
  };

  return JSON.stringify(jsonLd);
}

export function generateArticleJsonLd(data: ArticleJsonLd): string {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Article",
    headline: data.headline,
    description: data.description,
    ...(data.image && { image: data.image }),
    datePublished: data.datePublished,
    ...(data.dateModified && { dateModified: data.dateModified }),
    author: data.author,
    publisher: data.publisher,
    mainEntityOfPage: data.mainEntityOfPage,
    url: data.url,
    ...(data.articleSection && { articleSection: data.articleSection }),
    ...(data.keywords && { keywords: data.keywords }),
  };

  return JSON.stringify(jsonLd);
}

export function generateBreadcrumbJsonLd(data: BreadcrumbJsonLd): string {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: data.itemListElement,
  };

  return JSON.stringify(jsonLd);
}

export function generateWebApplicationJsonLd(data: WebApplicationJsonLd): string {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: data.name,
    description: data.description,
    url: data.url,
    applicationCategory: data.applicationCategory,
    operatingSystem: data.operatingSystem,
    ...(data.offers && { offers: data.offers }),
    ...(data.aggregateRating && { aggregateRating: data.aggregateRating }),
  };

  return JSON.stringify(jsonLd);
}

// Helper function to generate JSON-LD for hiking/outdoor equipment
export function generateProductJsonLd(product: {
  name: string;
  description: string;
  brand?: string;
  weight?: number;
  weightUnit?: string;
  category: string;
  image?: string;
  url: string;
}): string {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name,
    description: product.description,
    ...(product.brand && { brand: { "@type": "Brand", name: product.brand } }),
    ...(product.weight && product.weightUnit && {
      weight: {
        "@type": "QuantitativeValue",
        value: product.weight,
        unitCode: product.weightUnit.toUpperCase(),
      },
    }),
    category: product.category,
    ...(product.image && { image: product.image }),
    url: product.url,
  };

  return JSON.stringify(jsonLd);
}

// Helper function for hiking trail/location data
export function generatePlaceJsonLd(place: {
  name: string;
  description: string;
  address?: string;
  geo?: {
    latitude: number;
    longitude: number;
  };
  url: string;
}): string {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Place",
    name: place.name,
    description: place.description,
    ...(place.address && { address: place.address }),
    ...(place.geo && {
      geo: {
        "@type": "GeoCoordinates",
        latitude: place.geo.latitude,
        longitude: place.geo.longitude,
      },
    }),
    url: place.url,
  };

  return JSON.stringify(jsonLd);
}

// Helper to create default website JSON-LD for the app
export function getDefaultWebsiteJsonLd(locale: string): WebsiteJsonLd {
  const baseUrl = process.env.NEXT_PUBLIC_FRONT_URL || "";
  
  return {
    name: "Hike A Good Day",
    description: locale === "ko" 
      ? "당신의 가벼운 하이킹 동반자 - 장비 무게 측정 및 관리 플랫폼"
      : "Your lightweight hiking companion - gear weight tracking and management platform",
    url: `${baseUrl}/${locale}`,
    logo: `${baseUrl}/logo-l.png`,
    sameAs: [
      // Add social media links when available
    ],
    potentialAction: {
      "@type": "SearchAction",
      target: `${baseUrl}/${locale}/posts?search={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  };
}
