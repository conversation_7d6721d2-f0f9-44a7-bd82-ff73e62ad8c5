import { Metadata } from "next";

export interface SEOConfig {
  title: string;
  description: string;
  url: string;
  locale: string;
  siteName?: string;
  images?: Array<{
    url: string;
    width?: number;
    height?: number;
    alt?: string;
  }>;
  type?: "website" | "article";
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  tags?: string[];
  section?: string;
  keywords?: string[];
  canonical?: string;
  alternateLanguages?: Record<string, string>;
  noindex?: boolean;
  nofollow?: boolean;
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    url,
    locale,
    siteName = "Hike A Good Day",
    images = [],
    type = "website",
    publishedTime,
    modifiedTime,
    authors,
    tags,
    section,
    keywords,
    canonical,
    alternateLanguages,
    noindex = false,
    nofollow = false,
  } = config;

  // Default image if none provided
  const defaultImage = {
    url: `${process.env.NEXT_PUBLIC_FRONT_URL}/logo-l.png`,
    width: 1200,
    height: 630,
    alt: siteName,
  };

  const finalImages = images.length > 0 ? images : [defaultImage];

  const metadata: Metadata = {
    title,
    description,
    keywords: keywords?.join(", "),
    openGraph: {
      title,
      description,
      url,
      siteName,
      images: finalImages,
      locale,
      type,
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(authors && { authors }),
      ...(tags && { tags }),
      ...(section && { section }),
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: finalImages.map(img => img.url),
    },
    ...(canonical && {
      alternates: {
        canonical,
        ...(alternateLanguages && { languages: alternateLanguages }),
      },
    }),
    ...(alternateLanguages && !canonical && {
      alternates: {
        languages: alternateLanguages,
      },
    }),
    ...(noindex || nofollow) && {
      robots: {
        index: !noindex,
        follow: !nofollow,
        googleBot: {
          index: !noindex,
          follow: !nofollow,
        },
      },
    },
  };

  return metadata;
}

// Helper function to generate hreflang alternates
export function generateAlternateLanguages(
  basePath: string,
  supportedLocales: string[] = ["en", "ko"]
): Record<string, string> {
  const alternates: Record<string, string> = {};
  
  supportedLocales.forEach(locale => {
    alternates[locale] = `/${locale}${basePath}`;
  });
  
  return alternates;
}

// Helper function to create SEO-friendly URLs
export function createSEOUrl(baseUrl: string, locale: string, path: string): string {
  return `${baseUrl}/${locale}${path}`;
}

// Helper function to truncate description to optimal length
export function truncateDescription(description: string, maxLength: number = 160): string {
  if (description.length <= maxLength) {
    return description;
  }
  
  return description.substring(0, maxLength - 3).trim() + "...";
}

// Helper function to generate keywords from content
export function generateKeywords(
  content: string,
  additionalKeywords: string[] = [],
  maxKeywords: number = 10
): string[] {
  // Basic keyword extraction (in a real app, you might use a more sophisticated approach)
  const words = content
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter(word => word.length > 3)
    .filter(word => !["this", "that", "with", "have", "will", "from", "they", "been", "were", "said", "each", "which", "their", "time", "would", "there", "could", "other", "more", "very", "what", "know", "just", "first", "into", "over", "think", "also", "your", "work", "life", "only", "can", "still", "should", "after", "being", "now", "made", "before", "here", "through", "when", "where", "much", "some", "these", "many", "then", "them", "well", "were"].includes(word));

  // Count word frequency
  const wordCount: Record<string, number> = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });

  // Sort by frequency and take top words
  const topWords = Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxKeywords - additionalKeywords.length)
    .map(([word]) => word);

  return [...additionalKeywords, ...topWords].slice(0, maxKeywords);
}

// Default SEO configuration for the app
export const defaultSEOConfig = {
  siteName: "Hike A Good Day",
  defaultImage: {
    url: `${process.env.NEXT_PUBLIC_FRONT_URL}/logo-l.png`,
    width: 1200,
    height: 630,
    alt: "Hike A Good Day",
  },
  supportedLocales: ["en", "ko"],
  baseUrl: process.env.NEXT_PUBLIC_FRONT_URL || "",
};
