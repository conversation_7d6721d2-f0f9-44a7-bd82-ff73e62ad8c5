import { Dispatch, SetStateAction } from "react";

/**
 * Updates a stateful list by modifying an existing item, updating a specific property,
 * or adding a new item.
 *
 * @template T - The type of elements in the list.
 * @param setter - The state setter function for updating the list.
 * @param value - The new value or partial update for the item.
 * @param idx - The index of the item to update (null to add a new item).
 * @param property - The specific property to update (null to replace the entire item).
 *
 * @example
 * // Example usage:
 * const [list, setList] = useState([{ name: "<PERSON>", age: 25 }, { name: "<PERSON>", age: 30 }]);
 *
 * // Update 'age' of index 0
 * onChangeListState(setList, 26, 0, "age");
 *
 * // Replace entire item at index 1
 * onChangeListState(setList, { name: "<PERSON>", age: 35 }, 1);
 *
 * // Add a new item
 * onChangeListState(setList, { name: "<PERSON>", age: 40 });
 */
export const onChangeListState = <T = any>(
  // setter: Dispatch<SetStateAction<(T | Partial<T>)[]>>,
  setter:
    | Dispatch<SetStateAction<T[]>>
    | Dispatch<SetStateAction<Partial<T>[]>>
    | Dispatch<SetStateAction<any[]>>,
  value: T | Partial<T> | any,
  idx: number | null = null,
  property: keyof T | null = null
) => {
  setter((prev: T | Partial<T> | any) => {
    const next = [...prev];
    if (idx !== null) {
      if (property !== null) {
        next[idx] = { ...next[idx], [property]: value };
      } else {
        next[idx] = value as T;
      }
    } else {
      next.push(value as T);
    }
    return next;
  });
};

export const onRemoveItemInListState = <T = any>(
  setter:
    | Dispatch<SetStateAction<T[]>>
    | Dispatch<SetStateAction<Partial<T>[]>>
    | Dispatch<SetStateAction<any[]>>,
  idx: number
) => {
  setter((prev: T | Partial<T> | any) => {
    const next = [...prev];

    next.splice(idx, 1);

    return next;
  });
};
