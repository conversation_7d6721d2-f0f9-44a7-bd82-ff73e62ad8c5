const weightConversionRates: Record<WeightUnit, Record<WeightUnit, number>> = {
  kg: { kg: 1, lb: 2.20462, g: 1000, oz: 35.274 },
  lb: { kg: 0.453592, lb: 1, g: 453.592, oz: 16 },
  g: { kg: 0.001, lb: 0.00220462, g: 1, oz: 0.035274 },
  oz: { kg: 0.0283495, lb: 0.0625, g: 28.3495, oz: 1 },
};

export const convertWeight = (
  val: number,
  from: WeightUnit,
  to: WeightUnit
): number => {
  return val * weightConversionRates[from][to];
};

const distanceConversionRates: Record<
  DistanceUnit,
  Record<DistanceUnit, number>
> = {
  m: {
    m: 1,
    cm: 100,
    mm: 1000,
    km: 0.001,
    inch: 39.3701,
    ft: 3.28084,
    yd: 1.09361,
    mile: 0.000621371,
  },
  cm: {
    m: 0.01,
    cm: 1,
    mm: 10,
    km: 0.00001,
    inch: 0.393701,
    ft: 0.0328084,
    yd: 0.0109361,
    mile: 0.0000062137,
  },
  mm: {
    m: 0.001,
    cm: 0.1,
    mm: 1,
    km: 0.000001,
    inch: 0.0393701,
    ft: 0.00328084,
    yd: 0.00109361,
    mile: 0.000000621371,
  },
  km: {
    m: 1000,
    cm: 100000,
    mm: 1000000,
    km: 1,
    inch: 39370.1,
    ft: 3280.84,
    yd: 1093.61,
    mile: 0.621371,
  },
  inch: {
    m: 0.0254,
    cm: 2.54,
    mm: 25.4,
    km: 0.0000254,
    inch: 1,
    ft: 0.0833333,
    yd: 0.0277778,
    mile: 0.0000157828,
  },
  ft: {
    m: 0.3048,
    cm: 30.48,
    mm: 304.8,
    km: 0.0003048,
    inch: 12,
    ft: 1,
    yd: 0.333333,
    mile: 0.000189394,
  },
  yd: {
    m: 0.9144,
    cm: 91.44,
    mm: 914.4,
    km: 0.0009144,
    inch: 36,
    ft: 3,
    yd: 1,
    mile: 0.000568182,
  },
  mile: {
    m: 1609.34,
    cm: 160934,
    mm: 1609340,
    km: 1.60934,
    inch: 63360,
    ft: 5280,
    yd: 1760,
    mile: 1,
  },
};

export const convertDistance = (
  val: number,
  from: DistanceUnit,
  to: DistanceUnit
) => {
  return val * distanceConversionRates[from][to];
};
