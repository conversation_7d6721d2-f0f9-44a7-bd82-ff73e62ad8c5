import { format, isSameDay } from "date-fns";

export const timeAgo = (dateString: string): string => {
  const now = new Date();
  const past = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

  const secondsInMinute = 60;
  const secondsInHour = 60 * secondsInMinute;
  const secondsInDay = 24 * secondsInHour;
  const secondsInMonth = 30 * secondsInDay;
  const secondsInYear = 12 * secondsInMonth;

  if (diffInSeconds < 60) return "방금 전";
  if (diffInSeconds < secondsInHour)
    return `${Math.floor(diffInSeconds / secondsInMinute)}분 전`;
  if (diffInSeconds < secondsInDay)
    return `${Math.floor(diffInSeconds / secondsInHour)}시간 전`;
  if (diffInSeconds < secondsInMonth)
    return `${Math.floor(diffInSeconds / secondsInDay)}일 전`;
  if (diffInSeconds < secondsInYear)
    return `${Math.floor(diffInSeconds / secondsInMonth)}개월 전`;
  return `${Math.floor(diffInSeconds / secondsInYear)}년 전`;
};

export const getStartEndString = (
  startAt?: Date | string,
  endAt?: Date | string,
  formatString: string = "P"
): string => {
  if (!startAt && endAt) {
    return `~${format(endAt, formatString)}`;
  } else if (startAt && !endAt) {
    return `${format(startAt, formatString)}~`;
  } else if (startAt && endAt) {
    return `${isSameDay(startAt, endAt) ? `${format(startAt, formatString)}` : `${format(startAt, formatString)}~${format(endAt, formatString)}`}`;
  }
  return "-";
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}.${month}.${day}`;
};
