import { stringify } from "querystring";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "";

export const getFileUrl = ({ fileId, fileSn = 1 }: FileType) =>
  `${API_URL}/v1/files/${fileId}?${stringify({ fileSn })}`;
/**
 * Extracts fileSn from a given file URL.
 *
 * @param {string} fileUrl - The file URL.
 * @returns {number | null} The extracted fileSn, or null if not found.
 */
export const getFileSnFromFileUrl = (fileUrl?: string): number | null => {
  if (!fileUrl) {
    return null;
  }
  try {
    const url = new URL(fileUrl);
    const fileSnParam = url.searchParams.get("fileSn");
    if (!fileSnParam) {
      return 1;
    }
    return fileSnParam ? Number(fileSnParam) : null;
  } catch (error) {
    console.error("Invalid file URL:", fileUrl, error);
    return null;
  }
};

export const getFileIdFromFiles = (files: FileType[]): string | null => {
  if (files && files.length > 0) {
    const [file] = files;
    const { fileId, fileSn } = file;
    return fileId;
  }
  return null;
};

export const getFileSnFromFile = (file: FileType): number | null => {
  if (file) {
    const { fileId, fileSn } = file;
    return fileSn!;
  }
  return null;
};
