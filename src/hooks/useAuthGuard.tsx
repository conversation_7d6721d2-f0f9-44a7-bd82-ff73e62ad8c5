import { useEffect } from "react";
import { useRouter } from "@/i18n/routing";
import { FRONT_URL_PATH } from "@/lib/constants";
import { useUserStore } from "@/store/userStore";

/**
 * Hook to check if a user is authenticated.
 * Redirects to sign-in page if not authenticated.
 *
 * @returns {boolean} isAuthenticated - True if the user is logged in.
 */
export const useAuthGuard = (moveIfUnauthenticated = true) => {
  const user = useUserStore((state) => state.user);
  const _prepared = useUserStore((state) => state._prepared);
  const router = useRouter();

  useEffect(() => {
    if (_prepared && !user && moveIfUnauthenticated) {
      router.push(FRONT_URL_PATH.SIGNIN);
    }
  }, [_prepared, user]);

  return _prepared && !!user;
};
