#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Load translation files
const enPath = path.join(__dirname, '../lib/messages/en.json');
const koPath = path.join(__dirname, '../lib/messages/ko.json');

const enTranslations = JSON.parse(fs.readFileSync(enPath, 'utf8'));
const koTranslations = JSON.parse(fs.readFileSync(koPath, 'utf8'));

// Function to flatten nested objects
function flattenObject(obj, prefix = '') {
  const flattened = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  
  return flattened;
}

// Get all translation keys
const allKeys = new Set([
  ...Object.keys(flattenObject(enTranslations)),
  ...Object.keys(flattenObject(koTranslations))
]);

// Find all TypeScript and JavaScript files
const sourceFiles = [
  ...glob.sync('src/**/*.{ts,tsx,js,jsx}', { cwd: path.join(__dirname, '..') }),
].map(file => path.join(__dirname, '..', file));

// Track usage of translation keys
const usedKeys = new Set();
const usagePatterns = new Map();

// Regex patterns to find translation usage
const patterns = [
  // useTranslations("namespace")
  /useTranslations\s*\(\s*["']([^"']+)["']\s*\)/g,
  // getTranslations("namespace")
  /getTranslations\s*\(\s*["']([^"']+)["']\s*\)/g,
  // t("key")
  /\bt\s*\(\s*["']([^"']+)["']\s*\)/g,
];

sourceFiles.forEach(filePath => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const key = match[1];
        
        if (pattern.source.includes('useTranslations') || pattern.source.includes('getTranslations')) {
          // This is a namespace usage
          const namespace = key;
          if (!usagePatterns.has(namespace)) {
            usagePatterns.set(namespace, new Set());
          }
          
          // Find all t("key") calls in this file that might use this namespace
          const tPattern = /\bt\s*\(\s*["']([^"']+)["']\s*\)/g;
          let tMatch;
          while ((tMatch = tPattern.exec(content)) !== null) {
            const fullKey = `${namespace}.${tMatch[1]}`;
            usedKeys.add(fullKey);
            usagePatterns.get(namespace).add(tMatch[1]);
          }
        } else {
          // Direct key usage
          usedKeys.add(key);
        }
      }
    });
  } catch (error) {
    console.warn(`Warning: Could not read file ${filePath}:`, error.message);
  }
});

// Find unused keys
const unusedKeys = [];
allKeys.forEach(key => {
  if (!usedKeys.has(key)) {
    // Check if any parent namespace is used
    const parts = key.split('.');
    let isUsed = false;
    
    for (let i = 1; i < parts.length; i++) {
      const namespace = parts.slice(0, i).join('.');
      const subKey = parts.slice(i).join('.');
      
      if (usagePatterns.has(namespace) && usagePatterns.get(namespace).has(subKey)) {
        isUsed = true;
        break;
      }
    }
    
    if (!isUsed) {
      unusedKeys.push(key);
    }
  }
});

// Report results
console.log('=== Translation Usage Analysis ===\n');

console.log(`📊 Total translation keys: ${allKeys.size}`);
console.log(`✅ Used keys: ${usedKeys.size}`);
console.log(`❌ Potentially unused keys: ${unusedKeys.length}\n`);

if (unusedKeys.length > 0) {
  console.log('🚨 Potentially unused translation keys:');
  unusedKeys.forEach(key => {
    console.log(`  - ${key}`);
  });
  console.log('');
}

// Show namespace usage
console.log('📋 Translation namespace usage:');
usagePatterns.forEach((keys, namespace) => {
  console.log(`  ${namespace}: ${keys.size} keys used`);
  if (keys.size <= 5) {
    console.log(`    Keys: ${Array.from(keys).join(', ')}`);
  }
});

console.log('\n=== End Analysis ===');

// Note about limitations
console.log('\n⚠️  Note: This analysis may not catch all dynamic key usage or keys used in templates.');
console.log('Please manually verify unused keys before removing them.');
