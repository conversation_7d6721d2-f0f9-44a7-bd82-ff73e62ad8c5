#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Load translation files
const enPath = path.join(__dirname, '../lib/messages/en.json');
const koPath = path.join(__dirname, '../lib/messages/ko.json');

const enTranslations = JSON.parse(fs.readFileSync(enPath, 'utf8'));
const koTranslations = JSON.parse(fs.readFileSync(koPath, 'utf8'));

// Function to flatten nested objects
function flattenObject(obj, prefix = '') {
  const flattened = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  
  return flattened;
}

// Function to get nested value
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Function to set nested value
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

// Flatten both translation objects
const flatEnTranslations = flattenObject(enTranslations);
const flatKoTranslations = flattenObject(koTranslations);

// Find missing keys
const missingInKorean = [];
const missingInEnglish = [];

// Check for keys missing in Korean
for (const key in flatEnTranslations) {
  if (!(key in flatKoTranslations)) {
    missingInKorean.push({
      key,
      englishValue: flatEnTranslations[key]
    });
  }
}

// Check for keys missing in English
for (const key in flatKoTranslations) {
  if (!(key in flatEnTranslations)) {
    missingInEnglish.push({
      key,
      koreanValue: flatKoTranslations[key]
    });
  }
}

// Report results
console.log('=== Translation Validation Report ===\n');

if (missingInKorean.length > 0) {
  console.log('🚨 Keys missing in Korean translation:');
  missingInKorean.forEach(({ key, englishValue }) => {
    console.log(`  - ${key}: "${englishValue}"`);
  });
  console.log('');
}

if (missingInEnglish.length > 0) {
  console.log('🚨 Keys missing in English translation:');
  missingInEnglish.forEach(({ key, koreanValue }) => {
    console.log(`  - ${key}: "${koreanValue}"`);
  });
  console.log('');
}

if (missingInKorean.length === 0 && missingInEnglish.length === 0) {
  console.log('✅ All translation keys are present in both languages!');
} else {
  console.log(`Total issues found: ${missingInKorean.length + missingInEnglish.length}`);
  
  // Auto-fix missing keys by adding placeholders
  if (process.argv.includes('--fix')) {
    console.log('\n🔧 Auto-fixing missing keys...');
    
    let koUpdated = false;
    let enUpdated = false;
    
    // Add missing keys to Korean with placeholder
    missingInKorean.forEach(({ key, englishValue }) => {
      setNestedValue(koTranslations, key, `[TODO: Translate] ${englishValue}`);
      koUpdated = true;
    });
    
    // Add missing keys to English with placeholder
    missingInEnglish.forEach(({ key, koreanValue }) => {
      setNestedValue(enTranslations, key, `[TODO: Translate] ${koreanValue}`);
      enUpdated = true;
    });
    
    // Write updated files
    if (koUpdated) {
      fs.writeFileSync(koPath, JSON.stringify(koTranslations, null, 2) + '\n');
      console.log('✅ Updated Korean translations');
    }
    
    if (enUpdated) {
      fs.writeFileSync(enPath, JSON.stringify(enTranslations, null, 2) + '\n');
      console.log('✅ Updated English translations');
    }
    
    console.log('\n⚠️  Please review and translate the [TODO: Translate] placeholders!');
  } else {
    console.log('\n💡 Run with --fix flag to automatically add missing keys with placeholders');
  }
}

console.log('\n=== End Report ===');

// Exit with error code if there are missing keys
process.exit(missingInKorean.length + missingInEnglish.length > 0 ? 1 : 0);
