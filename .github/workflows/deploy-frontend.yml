name: Deploy Next.js 15 to <PERSON><PERSON> EC2

on:
  push:
    branches:
      - master
      - develop

  workflow_dispatch: # Allows manual trigger from GitHub Actions UI

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set Deployment Variables
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "EC2_USER=ec2-user" >> $GITHUB_ENV
            echo "EC2_HOST=${{ secrets.PROD_AWS_HOST }}" >> $GITHUB_ENV
            echo "DEPLOY_PATH=/home/<USER>/hike-a-good-day-app" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "EC2_USER=ec2-user" >> $GITHUB_ENV
            echo "EC2_HOST=${{ secrets.DEV_AWS_HOST }}" >> $GITHUB_ENV
            echo "DEPLOY_PATH=/home/<USER>/workspace/hike-a-good-day-app" >> $GITHUB_ENV
          fi

      - name: Make pem file
        run: |
          echo "${{ secrets.PROD_AWS_SSH_KEY }}" > private_key.pem
          chmod 400 private_key.pem

      - name: Safely Clean Deployment Directory on EC2
        run: |
          ssh -i private_key.pem -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST << EOF
          mkdir -p $DEPLOY_PATH
          rm -rf $DEPLOY_PATH/src $DEPLOY_PATH/public $DEPLOY_PATH/.next
          rm -f $DEPLOY_PATH/package.json $DEPLOY_PATH/package-lock.json
          rm -f $DEPLOY_PATH/next.config.ts $DEPLOY_PATH/eslint.config.mjs
          rm -f $DEPLOY_PATH/tailwind.config.ts $DEPLOY_PATH/tsconfig.json
          rm -f $DEPLOY_PATH/postcss.config.mjs
          rm -f $DEPLOY_PATH/next-sitemap.config.js
          EOF

      - name: Copy Source Files to EC2
        run: |
          scp -o StrictHostKeyChecking=no -i private_key.pem -r \
          src public package.json package-lock.json next.config.ts eslint.config.mjs \
          tailwind.config.ts tsconfig.json postcss.config.mjs next-sitemap.config.js \
          $EC2_USER@$EC2_HOST:$DEPLOY_PATH

      - name: Build & Restart Next.js App on EC2
        run: |
          ssh -i private_key.pem -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST << EOF
          cd $DEPLOY_PATH
          npm install # Install only production dependencies
          npm run build
          pm2 stop all || true
          pm2 start "yarn start"
          EOF

      - name: Cleanup SSH Key
        run: rm -f private_key.pem
