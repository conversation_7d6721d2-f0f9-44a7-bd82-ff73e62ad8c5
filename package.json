{"name": "boilerplate-nextjs15", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/accessibility": "^3.1.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "axios": "^1.7.9", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "framer-motion": "^12.4.7", "next": "15.1.7", "next-intl": "^3.26.5", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-quill-new": "^3.3.3", "styled-components": "^6.1.15", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "20.17.19", "@types/react": "19.0.10", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.24.1", "eslint": "^9", "eslint-config-next": "15.1.7", "eslint-plugin-prettier": "^5.2.3", "next-sitemap": "^4.2.3", "postcss": "^8", "prettier": "^3.5.2", "tailwindcss": "^3.4.1", "typescript": "5.7.3"}}