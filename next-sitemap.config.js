/** @type {import('next-sitemap').IConfig} */
const isProd = process.env.NEXT_PUBLIC_PROFILE === "production";
const SUPPORTED_LOCALES = ["en", "ko"]; // ← customize your supported locales
const BASE_URL = `${process.env.NEXT_PUBLIC_FRONT_URL}`; // ← replace with your domain
const API_URL = `${process.env.NEXT_PUBLIC_API_URL}`; // ← replace with your domain

const STATIC_PATHS = [
  "", // homepage
  "gear",
  "gear/measure",
  "gear/preset",
  "posts",
  "reviews",
  "suggest",
];
const EXCLUDED_PATH_PATHS = [
  "/signin",
  "/signup",
  "/my",
  "/settings",
  "/(ko|en)/signin",
  "/(ko|en)/signup",
  "/(ko|en)/my",
  "/(ko|en)/settings",
];

module.exports = {
  siteUrl: BASE_URL,
  generateRobotsTxt: true,
  sitemapSize: 7000, // sitemap별 최대 크기 (최대 크기가 넘어갈 경우 복수개의 sitemap으로 분리됨)
  changefreq: "daily",
  priority: 1,
  robotsTxtOptions: {
    // 정책 설정
    policies: isProd
      ? [
          {
            userAgent: "*", // 모든 agent 허용
            allow: "/", // 모든 페이지 주소 크롤링 허용
            disallow: EXCLUDED_PATH_PATHS,
          },
          // 추가 정책이 필요할 경우 배열 요소로 추가 작성
        ]
      : [
          {
            userAgent: "*",
            disallow: "/",
          },
        ],
  }, // robots.txt 옵션 설정
  exclude: EXCLUDED_PATH_PATHS,
  alternateRefs: SUPPORTED_LOCALES.map((locale) => ({
    href: `${BASE_URL}/${locale}`,
    hreflang: locale,
  })),
  transform: async (config, path) => {
    // Skip excluded paths
    if (
      config.exclude &&
      config.exclude.some((excludedPath) =>
        new RegExp(`^${excludedPath}`).test(path)
      )
    ) {
      return null;
    }

    return {
      loc: path,
      changefreq: "weekly",
      priority: 0.7,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async (config) => {
    console.log("IS PROFILE Production?: " + isProd);
    // Dynamically generate paths like /posts/[postId]
    const localizedPaths = [];

    for (const locale of SUPPORTED_LOCALES) {
      // Static paths
      for (const staticPath of STATIC_PATHS) {
        localizedPaths.push({
          loc: `/${locale}/${staticPath}`,
          changefreq: "weekly",
          priority: 0.9,
          lastmod: new Date().toISOString(),
        });
      }
      // Dynamic post paths
      const res = await fetch(`${API_URL}/v1/posts/id-list?locale=${locale}`);
      const { data } = await res.json();
      const { items } = data;
      for (const postId of items) {
        localizedPaths.push({
          loc: `/${locale}/posts/${postId}`,
          changefreq: "daily",
          priority: 0.8,
          lastmod: new Date().toISOString(),
        });
      }
    }
    return localizedPaths;
  },
};
